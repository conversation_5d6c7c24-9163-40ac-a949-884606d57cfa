/* eslint-disable max-lines, max-len */

export default {
    i18n: {
        de: {
            routes: {
                neuigkeiten: {
                    canonical: 'http://localhost:3000/neuigkeiten',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        },
                        {
                            _key:        '7eb8e59dc8e9',
                            _type:       'articleHeadingSection',
                            author:      'Ahjhafhdhfa',
                            description: 'fesdafdsafds',
                            heading:     'asdasd',
                            image:       {
                                _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                _type: 'image',
                                asset: {
                                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'asdasdd',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'elektronische-fuehrerschein-kontrolle',
                            publishedOn:   '2020-07-09',
                            year:          2020
                        }
                    ],
                    description: 'News Meta description',
                    pagination:  [
                        {
                            pageSlug: 'neuigkeiten/2021',
                            year:     '2021'
                        },
                        {
                            pageSlug: 'neuigkeiten/2020',
                            year:     '2020'
                        }
                    ]
                },
                'neuigkeiten/2020': {
                    canonical: 'http://localhost:3000/neuigkeiten/2020',
                    content:   [
                        {
                            _key:        '7eb8e59dc8e9',
                            _type:       'articleHeadingSection',
                            author:      'Ahjhafhdhfa',
                            description: 'fesdafdsafds',
                            heading:     'asdasd',
                            image:       {
                                _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                _type: 'image',
                                asset: {
                                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'asdasdd',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'elektronische-fuehrerschein-kontrolle',
                            publishedOn:   '2020-07-09',
                            year:          2020
                        }
                    ],
                    description: 'News Meta description 2020',
                    pagination:  [
                        {
                            pageSlug: 'neuigkeiten/2021',
                            year:     '2021'
                        },
                        {
                            pageSlug: 'neuigkeiten/2020',
                            year:     '2020'
                        }
                    ]
                },
                'neuigkeiten/2021': {
                    canonical: 'http://localhost:3000/neuigkeiten/2021',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        }
                    ],
                    description: 'News Meta description 2021',
                    pagination:  [
                        {
                            pageSlug: 'neuigkeiten/2021',
                            year:     '2021'
                        },
                        {
                            pageSlug: 'neuigkeiten/2020',
                            year:     '2020'
                        }
                    ]
                }
            }
        },
        en: {
            routes: {
                news: {
                    canonical: 'http://localhost:3000/news',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        },
                        {
                            _key:        '7eb8e59dc8e9',
                            _type:       'articleHeadingSection',
                            author:      'Ahjhafhdhfa',
                            description: 'fesdafdsafds',
                            heading:     'asdasd',
                            image:       {
                                _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                _type: 'image',
                                asset: {
                                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'asdasdd',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'elektronische-fuehrerschein-kontrolle',
                            publishedOn:   '2020-07-09',
                            year:          2020
                        }
                    ],
                    description: 'News Meta description',
                    pagination:  [
                        {
                            pageSlug: 'news/2021',
                            year:     '2021'
                        },
                        {
                            pageSlug: 'news/2020',
                            year:     '2020'
                        }
                    ]
                },
                'news/2020': {
                    canonical: 'http://localhost:3000/news/2020',
                    content:   [
                        {
                            _key:        '7eb8e59dc8e9',
                            _type:       'articleHeadingSection',
                            author:      'Ahjhafhdhfa',
                            description: 'fesdafdsafds',
                            heading:     'asdasd',
                            image:       {
                                _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                _type: 'image',
                                asset: {
                                    _ref:  'image-066ffe06e162cf172b454ce83883909fb3c5ac3b-1136x852-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'asdasdd',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'elektronische-fuehrerschein-kontrolle',
                            publishedOn:   '2020-07-09',
                            year:          2020
                        }
                    ],
                    description: 'News Meta description 2020',
                    pagination:  [
                        {
                            pageSlug: 'news/2021',
                            year:     '2021'
                        },
                        {
                            pageSlug: 'news/2020',
                            year:     '2020'
                        }
                    ]
                },
                'news/2021': {
                    canonical: 'http://localhost:3000/news/2021',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        }
                    ],
                    description: 'News Meta description 2021',
                    pagination:  [
                        {
                            pageSlug: 'news/2021',
                            year:     '2021'
                        },
                        {
                            pageSlug: 'news/2020',
                            year:     '2020'
                        }
                    ]
                }
            }
        },
        es: {
            routes: {
                noticias: {
                    canonical: 'http://localhost:3000/noticias',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        }
                    ],
                    description: 'News Meta description',
                    pagination:  [
                        {
                            pageSlug: 'noticias/2021',
                            year:     '2021'
                        }
                    ]
                },
                'noticias/2021': {
                    canonical: 'http://localhost:3000/noticias/2021',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        }
                    ],
                    description: 'News Meta description 2021',
                    pagination:  [
                        {
                            pageSlug: 'noticias/2021',
                            year:     '2021'
                        }
                    ]
                }
            }
        },
        nl: {
            routes: {
                nieuws: {
                    canonical: 'http://localhost:3000/nieuws',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        }
                    ],
                    description: 'News Meta description',
                    pagination:  [
                        {
                            pageSlug: 'nieuws/2021',
                            year:     '2021'
                        }
                    ]
                },
                'nieuws/2021': {
                    canonical: 'http://localhost:3000/nieuws/2021',
                    content:   [
                        {
                            _key:        'c3cf0f2d1092',
                            _type:       'articleHeadingSection',
                            author:      'Ace Ventura',
                            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            heading:     'SOme heading',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-0c4541069612f52d2a47f9c5d16e90e52e8d195a-2500x1667-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'My car',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-12-05',
                            year:          2021
                        },
                        {
                            _key:        '5b4dabcbae3c',
                            _type:       'articleHeadingSection',
                            author:      'Papichulo',
                            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
                            heading:     'This is some news for you',
                            image:       {
                                _type: 'image',
                                asset: {
                                    _ref:  'image-20759ace134bbf45b13c660b55f3dcbb988d8b4a-1280x720-jpg',
                                    _type: 'reference'
                                }
                            },
                            imageAlt:      'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.',
                            includeInNews: true,
                            isIndex:       true,
                            pageSlug:      'v4/request/30-day-trial',
                            publishedOn:   '2021-07-01',
                            year:          2021
                        }
                    ],
                    description: 'News Meta description 2021',
                    pagination:  [
                        {
                            pageSlug: 'nieuws/2021',
                            year:     '2021'
                        }
                    ]
                }
            }
        }
    }
};

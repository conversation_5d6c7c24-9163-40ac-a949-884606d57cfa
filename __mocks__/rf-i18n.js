export function T({children}) {
    return children;
}

export function t(text) {
    return text;
}

export function n(number) {
    return number;
}

export function N({children}) {
    return children;
}

export function I18NextProvider({children}) {
    return children;
}

export let i18n = {
    isReady:   false,
    i18n:      jest.fn(),
    languages: ['en'],
    on:        () => jest.fn(),
    exists:    () => jest.fn().mockReturnValue(true)
};

export let languageList = [{code: 'en'}, {code: 'de'}];

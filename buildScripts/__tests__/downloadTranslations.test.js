describe('downloadTranslations', () => {
    const {config} = require('../../config');

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('removes the config translation path first', async() => {
        jest.mock('fs-extra', () => ({
            remove: jest.fn().mockResolvedValue(null)
        }));

        require('../downloadTranslations');
        await sleep();

        const fsExtra = require('fs-extra');

        expect(fsExtra.remove).toHaveBeenCalledWith(config.services.languages.translationsOutputPath);
    });

    test('catches errors', async() => {
        const mockError = new Error('test');

        const exitSpy = jest.spyOn(process, 'exit').mockImplementation(() => {});
        const logSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

        jest.mock('fs-extra', () => ({
            remove: jest.fn().mockRejectedValue(mockError)
        }));

        require('../downloadTranslations');
        await sleep();

        expect(logSpy).toHaveBeenCalledWith(mockError);
        expect(exitSpy).toHaveBeenCalledWith(1);
    });

    test('exports the language from the project', async() => {
        jest.mock('node-fetch', () => jest.fn().mockImplementation(path => {
            if (path.includes('language-codes')) {
                return Promise.resolve({
                    json: () => Promise.resolve(['de', 'en'])
                });
            }

            return Promise.resolve({
                json: () => Promise.resolve([{
                    label:        'something',
                    translations: {
                        en: 'Something',
                        de: 'Etwas'
                    }
                }])
            });
        }));

        jest.mock('fs-extra', () => ({
            remove:    jest.fn().mockResolvedValue(null),
            writeFile: jest.fn().mockResolvedValue(null)
        }));

        require('../downloadTranslations');
        await sleep();

        const fetch = require('node-fetch');
        const fsExtra = require('fs-extra');

        expect(fetch).toHaveBeenCalledTimes(2);
        expect(fsExtra.writeFile)
            .toHaveBeenCalledWith(config.services.languages.translationsOutputPath, JSON.stringify({
                something: 'Etwas'
            }, null, 4));
    });
});

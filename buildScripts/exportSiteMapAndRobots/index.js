#!/usr/bin/env node

const fs = require('fs-extra');
const {SitemapStream} = require('sitemap');

const {generateSitemap} = require('./generateSitemap');
const {generateRobots} = require('./generateRobots');

const {config} = require('../../config');

const hostname = config.host.url;

(async function exportSiteMapAndRobots() {
    const sitemap = new SitemapStream({hostname, cacheTime: 600000});

    await fs.remove('./public/sitemap.xml');
    await generateSitemap(sitemap);
    await generateRobots();

    console.log('sitemap.xml and robots.txt created');
}());

describe('exportSiteMapsAndRobots', () => {
    jest.mock('../generateSitemap');
    jest.mock('../generateRobots');
    jest.mock('fs-extra', () => ({remove: jest.fn()}));
    const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

    test('makes a call to generate the site map and robots', async() => {
        const {generateSitemap} = require('../generateSitemap');
        const {generateRobots} = require('../generateRobots');

        require('../index');
        await sleep();

        expect(generateSitemap).toHaveBeenCalled();
        expect(generateRobots).toHaveBeenCalled();
        expect(logSpy).toHaveBeenCalledWith('sitemap.xml and robots.txt created');
    });
});

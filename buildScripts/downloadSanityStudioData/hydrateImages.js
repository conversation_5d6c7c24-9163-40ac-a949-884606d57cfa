#!/usr/bin/env node
const _ = require('lodash');
const fs = require('fs-extra');

const {config} = require('../../config');

const sanityConfig = config.services.sanity;

function checkForImages(content, images) {
    _.forEach(content, value => {
        const ref = _.get(value, 'asset._ref');
        if (ref) {
            const dimensions = images[ref];

            if (dimensions) {
                value.width = dimensions.width;
                value.height = dimensions.height;
            }
        } else if (typeof value === 'object') {
            checkForImages(value, images);
        }
    });
}

async function saveHydrated({news, routes, changelog, siteConfig}) {
    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/news.json`, JSON.stringify(news));
    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/routes.json`, JSON.stringify(routes));
    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/changelog.json`, JSON.stringify(changelog));
    await fs.writeFile(`${sanityConfig.studioDataOutputPath}/siteConfig.json`, JSON.stringify(siteConfig));
}

async function hydrateImages() {
    const siteConfig = require(`${sanityConfig.studioDataOutputPath}/siteConfig.json`);
    const {language} = siteConfig;
    const routes = require(`${sanityConfig.newStudioDataOutputPath}/routes.json`);
    const news = require(`${sanityConfig.newStudioDataOutputPath}/news.json`);
    const changelog = require(`${sanityConfig.newStudioDataOutputPath}/changelog.json`);
    const images = require(`${sanityConfig.newStudioDataOutputPath}/images`);

    _.forEach(routes, route => {
        const content = _.get(route, `i18n.${language}.page.i18n.${language}.content`);

        if (content) { checkForImages(routes, images); }
    });

    _.forEach(news.i18n[language].routes, newsItem => {
        const content = newsItem.content;

        if (content) { checkForImages(routes, images); }
    });

    _.forEach(changelog.i18n[language].routes, changelogItem => {
        const content = changelogItem.content;

        if (content) { checkForImages(routes, images); }
    });

    checkForImages(siteConfig, images);

    await saveHydrated({news, routes, changelog, siteConfig});
}

module.exports = {hydrateImages};


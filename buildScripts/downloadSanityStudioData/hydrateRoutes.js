#!/usr/bin/env node
const _ = require('lodash');
const fs = require('fs-extra');

const {config} = require('../../config');

const sanityConfig = config.services.sanity;

function removeEndSlash(url) {
    if (typeof url !== 'string') {
        return url;
    }

    return url
        .replace(/([^:])\/\//g, '$1/')
        .replace(/\/$/, '');
}

function getRouteUrl({route, routes, language, host}) {
    const fullRoute = _.find(routes, {_id: _.get(route, '_ref')});
    let slugCurrent = _.get(fullRoute, `i18n.${language}.slug.current`);

    if (!slugCurrent) { return null; }

    slugCurrent = removeEndSlash(slugCurrent);

    return `${host.url}/${slugCurrent}`;
}

function checkForRoutes({content, routes, language, host}) {
    _.forEach(content, value => {
        if (value?._ref) {
            const href = getRouteUrl({route: value, routes, language, host});

            if (href) {
                value.href = href;
            }
        } else if (typeof value === 'object') {
            checkForRoutes({content: value, routes, language, host});
        }
    });
}

async function saveHydrated({routes, news, changelog, siteConfig}) {
    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/routes.json`, JSON.stringify(routes));
    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/news.json`, JSON.stringify(news));
    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/changelog.json`, JSON.stringify(changelog));
    await fs.writeFile(`${sanityConfig.studioDataOutputPath}/siteConfig.json`, JSON.stringify(siteConfig));
}

async function hydrateRoutes() {
    const siteConfig = require(`${sanityConfig.studioDataOutputPath}/siteConfig.json`);
    const {language, host} = siteConfig;
    const routes = require(`${sanityConfig.newStudioDataOutputPath}/routes.json`);
    const news = require(`${sanityConfig.newStudioDataOutputPath}/news.json`);
    const changelog = require(`${sanityConfig.newStudioDataOutputPath}/changelog.json`);

    _.forEach(routes, route => {
        const content = _.get(route, `i18n.${language}.page.i18n.${language}.content`);

        if (content) {
            checkForRoutes({content, routes, language, host});
        }
    });

    _.forEach(news.i18n[language].routes, route => {
        checkForRoutes({route, routes, language, host});
    });

    _.forEach(changelog.i18n[language].routes, route => {
        const content = route.content;

        if (content) {
            checkForRoutes({content, routes, language, host});
        }
    });

    checkForRoutes({content: siteConfig, routes, language, host});

    await saveHydrated({routes, news, changelog, siteConfig});
}

module.exports = {hydrateRoutes, removeEndSlash, getRouteUrl, checkForRoutes};


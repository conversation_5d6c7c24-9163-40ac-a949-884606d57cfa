const fs = require('fs-extra');
const _ = require('lodash');

const {fetchDocuments} = require('../../src/node_modules/sanity-client-adapter');
const {config} = require('../../config');

const sanityConfig = config.services.sanity;

async function writeImageData() {
    const imageQuery = `*[_type == "sanity.imageAsset"] {
        _id,
        metadata {
            dimensions {
                height,
                width
            }
        }
    }`;

    let images = await fetchDocuments(imageQuery);

    images = _.transform(images, (images, image) => {
        images[image._id] = image.metadata.dimensions;
    }, {});

    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/images.json`, JSON.stringify(images));
}

module.exports = {writeImageData};


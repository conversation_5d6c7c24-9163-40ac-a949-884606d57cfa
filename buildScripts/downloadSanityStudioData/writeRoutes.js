#!/usr/bin/env node
const fs = require('fs-extra');
const _ = require('lodash');

const {fetchDocuments, getDocument} = require('../../src/node_modules/sanity-client-adapter');
const {config} = require('../../config');

const sanityConfig = config.services.sanity;

async function getHomePageRoute(language, availableLanguages) {
    const siteConfig = require(`${sanityConfig.studioDataOutputPath}/siteConfig.json`);

    const currentLanguageHomePageRef = _.get(siteConfig, `i18n.${language}.homePage`);

    if (!currentLanguageHomePageRef) {
        return null;
    }

    const i18nRoutes = _.transform(availableLanguages, (routes, lang) => {
        const language = lang.label.toLowerCase();

        const homePageRef = _.get(siteConfig, `i18n.${language}.homePage._ref`);

        if (homePageRef) {
            routes[language] = {
                includeInSitemap: true,
                slug:             {current: ''},
                page:             {_ref: homePageRef}
            };
        }
    }, {});

    const homePageReference = i18nRoutes[language].page._ref;

    if (!homePageReference) {
        return null;
    }

    const homePage = await getDocument(homePageReference, language);
    i18nRoutes[language].page = homePage;

    return {
        _id:        'homePage',
        _updatedAt: homePage._updatedAt,
        i18n:       i18nRoutes
    };
}

async function getAllRoutes(language) {
    const routesQuery = '*[_type == "route" && !(_id match "drafts*") && language == $language] { _id, _createdAt, _updatedAt, language }';
    const pagesQuery = '*[_type == "page" && !(_id match "drafts*") && language == $language] { _id, _createdAt, _updatedAt, language }';

    let routes = await fetchDocuments(routesQuery, {language});
    const pages = await fetchDocuments(pagesQuery, {language}, language);

    for (const route of routes) {
        const page = _.find(pages, {_id: _.get(route, ['i18n', language, 'page', '_ref'])});

        if (page) {
            _.set(route, ['i18n', language, 'page'], page);
        }
    }

    return routes;
}

async function writeRoutes() {
    const language = config.language.toLowerCase();
    const languages = config.languages;

    let routes = await getAllRoutes(language);

    routes = _.transform(routes, (routes, route) => {
        const routeSlug = _.get(route, `i18n.${language}.slug.current`);

        if (routeSlug) { routes[routeSlug] = route; }
    }, {});

    const homePageRoute = await getHomePageRoute(language, languages);

    if (homePageRoute) {
        routes[''] = homePageRoute;
    }

    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/routes.json`, JSON.stringify(routes));
}

module.exports = {writeRoutes};


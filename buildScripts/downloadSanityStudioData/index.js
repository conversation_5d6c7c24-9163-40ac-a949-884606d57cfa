#!/usr/bin/env node
const fs = require('fs-extra');

const {config} = require('../../config');
const sanityConfig = config.services.sanity;

const {writeImageData} = require('./writeImageData');
const {writeRoutes} = require('./writeRoutes');
const {writeSiteConfig} = require('./writeSiteConfig');
const {writeChangelogs} = require('./writeChangelogs');
const {hydrateBreadCrumbs} = require('./hydrateBreadCrumbs');
const {writeNews} = require('./writeNews');
const {hydrateRoutes} = require('./hydrateRoutes');
const {hydrateImages} = require('./hydrateImages');
const {hydrateAsyncData} = require('./hydrateAsyncData');

async function hydrateData() {
    await hydrateBreadCrumbs();
    await hydrateRoutes();
    await hydrateImages();
    await hydrateAsyncData();
}

async function downloadSanityStudioData() {
    try {
        await fs.ensureDir(`${sanityConfig.newStudioDataOutputPath}`);

        await writeSiteConfig();
        await writeRoutes();
        await writeImageData();
        await writeChangelogs();
        await writeNews();
        await hydrateData();
    } catch (e) {
        console.error(e);
        // eslint-disable-next-line no-process-exit
        process.exit(1);
    }
}

module.exports = {downloadSanityStudioData};

if (require.main === module) {
    downloadSanityStudioData();
}

const _ = require('lodash');
const fs = require('fs-extra');

const {config} = require('../../config');

const sanityConfig = config.services.sanity;

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;

async function handleVideoSection(section) {
    const videoUrl = _.get(section, 'videoUrl.href', section.videoUrl);

    if (_.includes(videoUrl, 'www.youtube.com')) {
        const videoId = videoUrl.match(/watch\?v=(.+?)(&|$)/)[1];
        let searchResults;
        try {
            // eslint-disable-next-line max-len
            searchResults = await fetch(`https://www.googleapis.com/youtube/v3/videos?part=snippet,contentDetails,statistics&id=${videoId}&key=${GOOGLE_API_KEY}`);
            searchResults = await searchResults.json();
        } catch (error) {
            console.warn(error);
        }

        const videoData = {
            id: videoId,
            ..._.get(searchResults, 'items.0.snippet', {}),
            ..._.get(searchResults, 'items.0.contentDetails', {}),
            ..._.get(searchResults, 'items.0.statistics', {})
        };

        _.set(section, 'youtubeVideoData', videoData);
    }
}

const sectionHandlers = {
    videoSection: handleVideoSection,
    changelog:    handleVideoSection
};

async function hydrateContent(content) {
    for (const section of content) {
        if (sectionHandlers[section._type]) {
            await sectionHandlers[section._type](section);
        }
    }
}

async function hydrateAsyncData() {
    const {language} = require(`${sanityConfig.studioDataOutputPath}/siteConfig.json`);
    const routes = require(`${sanityConfig.newStudioDataOutputPath}/routes.json`);
    const changelog = require(`${sanityConfig.newStudioDataOutputPath}/changelog.json`);

    for (const route of _.values(routes)) {
        const content = _.get(route, `i18n.${language}.page.i18n.${language}.content`);

        if (content) {
            await hydrateContent(content);
        }
    }

    for (const route of _.values(changelog.i18n[language].routes)) {
        if (route.content) {
            await hydrateContent(route.content);
        }
    }

    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/routes.json`, JSON.stringify(routes));
    await fs.writeFile(`${sanityConfig.newStudioDataOutputPath}/changelog.json`, JSON.stringify(changelog));
}

module.exports = {
    hydrateAsyncData
};

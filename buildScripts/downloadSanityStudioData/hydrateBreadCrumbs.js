const fs = require('fs-extra');
const _ = require('lodash');

const {config} = require('../../config');
const sanityConfig = config.services.sanity;

const routesPath = `${sanityConfig.newStudioDataOutputPath}/routes.json`;
const newsPath = `${sanityConfig.newStudioDataOutputPath}/news.json`;
const changelogsPath = `${sanityConfig.newStudioDataOutputPath}/changelog.json`;

const {generateBreadCrumbsList} = require('./utils');

async function hydrateBreadCrumbs() {
    const {language} = require('../../src/node_modules/rf-data/siteConfig.json');
    const news = require(newsPath);
    const routes = require(routesPath);
    const changelogs = require(changelogsPath);

    _.forEach(routes, route => {
        const slug = route?.i18n?.[language]?.slug?.current;

        if (!slug) { return; }

        route.breadcrumbsList = generateBreadCrumbsList(slug);
    });

    _.forEach(news.i18n?.[language]?.routes, (route, routeKey) => {
        route.breadcrumbsList = generateBreadCrumbsList(routeKey);
    });

    _.forEach(changelogs.i18n?.[language]?.routes, (route, routeKey) => {
        route.breadcrumbsList = generateBreadCrumbsList(routeKey);
    });

    await fs.writeFile(routesPath, JSON.stringify(routes));
    await fs.writeFile(newsPath, JSON.stringify(news));
    await fs.writeFile(changelogsPath, JSON.stringify(changelogs));
}

module.exports = {hydrateBreadCrumbs};

const fs = require('fs-extra');
const _ = require('lodash');
const {hydrateRoutes, removeEndSlash, getRouteUrl, checkForRoutes} = require('../hydrateRoutes');

jest.mock('fs-extra');
jest.mock('../../../config');

const siteConfig = require('../../../config').config.services.sanity;

describe('hydrateRoutes', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should remove ending slashes from url', () => {
        expect(removeEndSlash('http://example.com//')).toBe('http://example.com');
        expect(removeEndSlash('http://example.com/')).toBe('http://example.com');
        expect(removeEndSlash('http://example.com')).toBe('http://example.com');
        expect(removeEndSlash(123)).toBe(123);
    });

    it('should get correct route url', () => {
        const routes = [{_id: 'route1', i18n: {en: {slug: {current: 'slug1'}}}}];
        const route = {_ref: 'route1'};
        const host = {url: 'http://example.com'};
        const language = 'en';

        expect(getRouteUrl({route, routes, language, host})).toBe('http://example.com/slug1');
    });

    it('should checkForRoutes and modify content', () => {
        const routes = [{_id: 'route1', i18n: {en: {slug: {current: 'slug1'}}}}];
        const content = [{_type: 'reference', _ref: 'route1'}];
        const host = {url: 'http://example.com'};
        const language = 'en';

        checkForRoutes({content, routes, language, host});

        expect(_.first(content).href).toBe('http://example.com/slug1');
    });

    it('should hydrate routes and write updated data', async() => {
        const routes = {testRoute: {i18n: {en: {page: {i18n: {en: {content: 'some content'}}}}}}};

        jest.mock(`${siteConfig.studioDataOutputPath}/siteConfig.json`, () => ({
            language: 'en',
            host:     {url: 'http://example.com'}
        }), {virtual: true});
        jest.mock(`${siteConfig.newStudioDataOutputPath}/routes.json`, () => routes, {virtual: true});

        const fsExtraMock = jest.spyOn(fs, 'writeFile').mockImplementation(() => Promise.resolve());

        await hydrateRoutes();

        expect(fsExtraMock).toHaveBeenCalledWith(`${siteConfig.newStudioDataOutputPath}/routes.json`, JSON.stringify(routes));
        expect(fsExtraMock).toHaveBeenCalledWith(
            `${siteConfig.studioDataOutputPath}/siteConfig.json`,
            JSON.stringify(require(`${siteConfig.studioDataOutputPath}/siteConfig.json`))
        );
    });
});

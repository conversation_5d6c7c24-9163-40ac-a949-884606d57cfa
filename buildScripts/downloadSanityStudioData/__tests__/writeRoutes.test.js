import {writeFile} from 'fs-extra';
import mockSanityClientAdapter, {fetchDocuments, getDocument} from 'sanity-client-adapter';

import siteConfig from '../../../__mocks__/siteConfig';
import {mockConfig} from '../../../config';

import {writeRoutes} from '../writeRoutes';

jest.mock('../../../src/node_modules/sanity-client-adapter', () => mockSanityClientAdapter);

jest.mock('fs-extra', () => ({
    writeFile: jest.fn()
}));

jest.mock('../../../config', () => {
    const mockConfig = jest.fn().mockReturnValue(jest.requireActual('../../../config').config);

    return {
        mockConfig,
        get config() {
            return {
                ...mockConfig(),
                services: {
                    sanity: {
                        studioDataOutputPath:    mockConfig().services.sanity.studioDataOutputPath,
                        newStudioDataOutputPath: 'studio-data-output-path'
                    }
                },
                get language() {
                    return mockConfig().language;
                }
            };
        }
    };
});

describe('writeRoutes', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    test('writes routes to file', async() => {
        const UPDATED_AT = '2020-01-01T00:00:00Z';
        getDocument.mockResolvedValue({_updatedAt: UPDATED_AT, _ref: 'page1'});
        fetchDocuments.mockResolvedValueOnce([
            {
                _id:        'route1', _createdAt: '2020-01-01T00:00:00Z',
                _updatedAt: UPDATED_AT,
                language:   'en',
                i18n:       {
                    de: {page: {_ref: 'page1'}, slug: {current: 'de-route1'}}
                }
            }
        ]);
        fetchDocuments.mockResolvedValueOnce([
            {_id: 'page1', _createdAt: '2020-01-01T00:00:00Z', _updatedAt: UPDATED_AT, language: 'de'}
        ]);

        await writeRoutes();

        expect(writeFile).toHaveBeenCalledTimes(1);
        expect(writeFile).toHaveBeenCalledWith('studio-data-output-path/routes.json', expect.any(String));

        const writtenRoutes = JSON.parse(writeFile.mock.calls[0][1]);
        expect(writtenRoutes).toMatchSnapshot();
    });

    test('adds home page route when page reference exists in site config', async() => {
        const UPDATED_AT = '2020-01-01T00:00:00Z';
        getDocument.mockResolvedValue({_updatedAt: UPDATED_AT, _ref: 'page1'});
        fetchDocuments.mockResolvedValue([]);

        await writeRoutes();

        const writtenRoutes = JSON.parse(writeFile.mock.calls[0][1]);

        console.log(writtenRoutes);

        expect(writtenRoutes['']).toBeDefined();
        expect(writtenRoutes['']._updatedAt).toEqual(UPDATED_AT);
        expect(writtenRoutes[''].i18n.de.page._ref).toEqual(siteConfig.i18n.de.homePage._ref);
        expect(writtenRoutes[''].i18n.de.slug.current).toEqual('');
        expect(writtenRoutes[''].i18n.en).toBeUndefined();
        expect(writtenRoutes[''].i18n.es.slug.current).toEqual('');
        expect(writtenRoutes[''].i18n.nl).toBeUndefined();
    });

    test('does not add home page route when not defined in site config', async() => {
        const UPDATED_AT = '2020-01-01T00:00:00Z';
        getDocument.mockResolvedValue({_updatedAt: UPDATED_AT, _ref: 'page1'});
        fetchDocuments.mockResolvedValue([]);

        const originalConfig = mockConfig();
        mockConfig.mockReturnValueOnce({
            ...originalConfig,
            language: 'en'
        });

        await writeRoutes();

        const writtenRoutes = JSON.parse(writeFile.mock.calls[0][1]);

        expect(writtenRoutes['']).toBeUndefined();
    });
});

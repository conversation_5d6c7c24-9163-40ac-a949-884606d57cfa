import fs from 'fs-extra';
import mockSanityClientAdapter, {fetchDocuments} from 'sanity-client-adapter';

import {config} from '../../../config';

import {writeSiteConfig} from '../writeSiteConfig';

jest.mock('../../../src/node_modules/sanity-client-adapter', () => mockSanityClientAdapter);

jest.mock('../defaultConfig.json', () => ({
    i18n: {en: {homePage: 'Home'}, de: {homePage: 'Zuhause'}}
}));

jest.mock('fs-extra', () => ({
    writeFile: jest.fn().mockResolvedValue(true)
}));

describe('writeSiteConfig', () => {
    beforeAll(() => {
        fetchDocuments.mockResolvedValue({
            logo:             'logo',
            footerLogo:       'footerLogo',
            i18n:             {en: {homePage: 'Home'}, de: {homePage: 'Zuhause'}},
            socialMediaLinks: {}
        });
    });

    it('calls the sanity client with the correct query and writes the config', async() => {
        await writeSiteConfig();

        expect(fs.writeFile).toHaveBeenCalledWith(
            `${config.services.sanity.studioDataOutputPath}/siteConfig.json`,
            expect.any(String)
        );
    });

    it('throws an error if sanity client fails', async() => {
        fetchDocuments.mockRejectedValue(new Error('Sanity client error'));

        await expect(writeSiteConfig()).rejects.toThrow('Sanity client error');
    });
});

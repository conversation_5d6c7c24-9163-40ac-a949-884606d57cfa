describe('hydrateBreadCrumbs', () => {
    beforeEach(() => {
        jest.mock('fs-extra', () => ({
            writeFile: jest.fn()
        }));
    });

    test('formats data correctly', async() => {
        const {writeFile} = require('fs-extra');
        const {hydrateBreadCrumbs} = require('../hydrateBreadCrumbs');

        await hydrateBreadCrumbs();

        const routesJson = JSON.parse(writeFile.mock.calls[0][1]);

        expect(routesJson).toMatchSnapshot();

        const newsJson = JSON.parse(writeFile.mock.calls[1][1]);
        expect(newsJson).toMatchSnapshot();

        const changelogsJson = JSON.parse(writeFile.mock.calls[2][1]);
        expect(changelogsJson).toMatchSnapshot();
    });
});

import {writeFile} from 'fs-extra';
import mockSanityClientAdapter, {fetchDocuments} from 'sanity-client-adapter';

import {changelogs as mockChangelogs} from '../__mocks__/changelogMock';
import {writeChangelogs} from '../writeChangelogs';
jest.mock('../../../src/node_modules/sanity-client-adapter', () => mockSanityClientAdapter);

jest.mock('fs-extra', () => ({
    writeFile: jest.fn()
}));

describe('writeChangelogs', () => {
    beforeAll(() => {
        fetchDocuments.mockResolvedValue(mockChangelogs);
    });

    test('formats data correctly', async() => {
        await writeChangelogs();

        const writtenJson = JSON.parse(writeFile.mock.calls[0][1]);

        expect(writtenJson).toMatchSnapshot();
    });
});

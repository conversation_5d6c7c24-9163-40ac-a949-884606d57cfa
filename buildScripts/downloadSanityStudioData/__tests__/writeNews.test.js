import {writeFile} from 'fs-extra';

import {writeNews} from '../writeNews';

import routes from '../../../sanityStudioData/routes.json';
jest.mock('fs-extra', () => ({
    writeFile: jest.fn()
}));

const routesNotIncludedInNews = _.filter(routes, route => !route.i18n.de?.includeInNews);
const routesIncludedInNews = _.filter(routes, route => route.i18n.de?.includeInNews);

const routesIncludedInNewsWithArticles = routesIncludedInNews
    .filter(route => _.find(route.i18n.de?.page.i18n.de?.content, {_type: 'articleHeadingSection'}));
const routesIncludedInNewsWithoutArticles = routesIncludedInNews
    .filter(route => !_.find(route.i18n.de?.page.i18n.de?.content, {_type: 'articleHeadingSection'}));

describe('writeNews', () => {
    test('formats data correctly', async() => {
        await writeNews();

        const writtenJson = JSON.parse(writeFile.mock.calls[0][1]);

        const deNews = _.cloneDeep(writtenJson.i18n.de.routes);
        const allDeNewsArticles = _.flatten(_.map(deNews, 'content'));

        const isAnyNonNewsRoutesIncluded = routesNotIncludedInNews
            .some(route => _.find(allDeNewsArticles, {pageSlug: route.i18n.de?.slug.current}));

        expect(isAnyNonNewsRoutesIncluded).toBe(false);

        const isAnyRoutesWithNoArticlesIncluded = routesIncludedInNewsWithoutArticles
            .some(route => _.find(allDeNewsArticles, {pageSlug: route.i18n.de?.slug.current}));

        expect(isAnyRoutesWithNoArticlesIncluded).toBe(false);

        const areAllNewsRoutesIncluded = routesIncludedInNewsWithArticles
            .every(route => _.find(allDeNewsArticles, {pageSlug: route.i18n.de?.slug.current}));

        expect(areAllNewsRoutesIncluded).toBe(true);

        expect(writtenJson).toMatchSnapshot();
    });

    test('generates sorted json in index news even when dates are unsorted', async() => {
        await writeNews();

        const writtenJson = JSON.parse(writeFile.mock.calls[0][1]);

        const deNews = writtenJson.i18n.de.routes.neuigkeiten;

        expect(deNews).toMatchSnapshot();
    });
});

import _ from 'lodash';

import mockSiteConfig from '../__mocks__/siteConfig.json';
import mockRoutes from '../__mocks__/routes.json';
import mockChangelog from '../__mocks__/changelog';
import mockNews from '../__mocks__/news';
import mockPhoneCodes from '../__mocks__/libphonenumber-js/metadata.min.json';

global.mockStore = {};
global.mockStoreFn = function mockStoreFn(key, value) {
    if (!global.mockStore[key]) {
        global.mockStore[key] = value || jest.fn();
    }

    return global.mockStore[key];
};

global.IS_REACT_ACT_ENVIRONMENT = true;
process.env.WEBSITE_URL = 'http://www.testing-env.net';
global.Date = class extends Date {
    constructor(...args) {
        if (args.length) {
            super(...args);
        } else {
            super('2023-12-01T00:00:00.000Z');
        }
    }
};

// Mocking fancy styled-jsx
function mockCss() {
    return '';
}

mockCss.css = () => '';
mockCss.global = () => '';
mockCss.resolve = () => ({className: 'resolved-class-name', styles: ''});

function setupTestRender() {
    const renderer = require('react-test-renderer');

    global.render = renderer.create;
    global.act = renderer.act;
    global._ = _;
    global.fetch = require('jest-fetch-mock');
    global.sleep = (ms = 100) => new Promise(resolve => {
        setTimeout(resolve, ms);
    });
}

// eslint-disable-next-line max-statements
function applyGlobalMocks() {
    global.dateNowSpy = jest.spyOn(Date, 'now').mockImplementation(() => new Date('2023-12-01T00:00:00.000Z').valueOf());
    setupTestRender();

    jest.mock('rf-data/siteConfig.json', () => mockSiteConfig, {virtual: true});
    jest.mock('../src/node_modules/rf-data/siteConfig.json', () => mockSiteConfig, {virtual: true});

    jest.mock('../sanityStudioData/routes.json', () => mockRoutes, {virtual: true});
    jest.mock('../sanityStudioData/changelog.json', () => mockChangelog, {virtual: true});
    jest.mock('../sanityStudioData/news.json', () => mockNews, {virtual: true});

    jest.mock('libphonenumber-js/metadata.min.json', () => mockPhoneCodes, {virtual: true});

    jest.mock('styled-jsx/css', () => ({
        __esModule: true,
        default:    mockCss,
        css:        mockCss.css,
        global:     mockCss.global,
        resolve:    mockCss.resolve
    }));

    jest.mock('react-dom', () => ({
        ...jest.requireActual('react-dom'),
        createPortal: node => node
    }));
}

applyGlobalMocks();

const RESET_MODULE_EXCEPTIONS = [
    'react'
];

let mockActualRegistry = {};

RESET_MODULE_EXCEPTIONS.forEach(moduleName => {
    jest.doMock(moduleName, () => {
        if (!mockActualRegistry[moduleName]) {
            mockActualRegistry[moduleName] = jest.requireActual(moduleName);
        }

        return mockActualRegistry[moduleName];
    });
});

beforeEach(() => {
    applyGlobalMocks();
});

afterAll(() => {
    mockStore = {};
});

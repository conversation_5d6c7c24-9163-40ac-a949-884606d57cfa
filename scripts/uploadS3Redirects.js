#!/usr/bin/env node
const AWS = require('aws-sdk');
const _ = require('lodash');

const {fetchDocuments} = require('../src/node_modules/sanity-client-adapter');
const {config} = require('../config');

const defaultBucketConfig = {
    signatureVersion: 'v4',
    endpoint:         'https://s3.eu-central-1.amazonaws.com',
    region:           'eu-central-1'
};

function getRedirects() {
    const routesQuery = '*[_type == "redirect"] {...}';

    return fetchDocuments(routesQuery);
}

function getBucketName() {
    const language = config.language.toLowerCase();

    let tld = language;
    if (tld === 'en') {
        tld = 'net';
    }

    return `website.fleetster.${tld}`;
}

function getParams(originalUrl, redirectUrl) {
    if (!_.startsWith(redirectUrl, 'http') && !_.startsWith(redirectUrl, '/')) {
        redirectUrl = `/${redirectUrl}`;
    }

    return {
        Key:                     `${originalUrl}/index.html`,
        Body:                    '',
        ContentType:             'text/html',
        WebsiteRedirectLocation: redirectUrl
    };
}

async function uploadRedirects(redirects) {
    const language = config.language.toLowerCase();

    const s3bucket = new AWS.S3({
        ...defaultBucketConfig,
        params: {Bucket: getBucketName()}
    });

    for (let redirect of redirects) {
        let originalUrl = _.get(redirect, `i18n.${language}.originalUrl.current`, '').trim();
        let redirectUrl = _.get(redirect, `i18n.${language}.redirectUrl.current`, '').trim();
        if (!originalUrl || !redirectUrl) {
            continue;
        }

        const params = getParams(originalUrl, redirectUrl);

        let result = await s3bucket.upload(params).promise();

        console.log(originalUrl, redirectUrl, !!result);
    }
}

async function uploadS3Redirects() {
    try {
        const redirects = await getRedirects();
        await uploadRedirects(redirects);
    } catch (e) {
        console.error(e);
        // eslint-disable-next-line no-process-exit
        process.exit(1);
    }
}

module.exports = {
    uploadS3Redirects
};

if (require.main === module) {
    uploadS3Redirects();
}

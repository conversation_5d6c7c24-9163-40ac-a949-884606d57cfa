// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`tagManagerScripts injects different script in production 1`] = `
"<script src="https://www.googletagmanager.com/gtm.js?id=GTM-5KKL79V"></script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5KKL79V');</script><noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5KKL79V" height="0" width="0" style="display: none; visibility: hidden;" title="Google Tag Manager"/></noscript>"
`;

exports[`tagManagerScripts injects tag manager scripts 1`] = `
"<script src="https://www.googletagmanager.com/gtm.js?id=GTM-5KKL79V"></script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5KKL79V');</script><noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5KKL79V" height="0" width="0" style="display: none; visibility: hidden;" title="Google Tag Manager"/></noscript>"
`;

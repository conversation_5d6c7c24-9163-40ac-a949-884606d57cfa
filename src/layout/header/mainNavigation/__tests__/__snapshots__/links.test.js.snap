// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Navigation links All the links render as expected with a route 1`] = `
<div
  className="icon-padding center-content"
  title="Contact us"
>
  <a
    aria-label="Contact us"
    href="http://localhost:3000/v4/corsoroute"
  >
    <span
      className="bp3-icon"
    >
      <div
        height={22}
        src="/v4/svgs/mail.svg"
        width="22px"
      />
    </span>
  </a>
  <style />
</div>
`;

exports[`Navigation links All the links render as expected with a route 2`] = `
[
  <a
    className="center-content"
    href="http://localhost:3000/v4/corsoroute"
  >
    <span
      className="bp3-icon"
    >
      <div
        height={22}
        src="/v4/svgs/whiteLabel.svg"
        width="22px"
      />
    </span>
    Demo
  </a>,
  <style />,
]
`;

exports[`Navigation links All the links render as expected with a route 3`] = `
[
  <a
    className="center-content"
    href="http://localhost:3000/v4/corsoroute"
  >
    <span
      className="bp3-icon"
    >
      <div
        height={20}
        src="/v4/svgs/loginRound.svg"
        width="20px"
      />
    </span>
    Login
  </a>,
  <style />,
]
`;

exports[`Navigation links All the links render as expected with a url 1`] = `
<div
  className="icon-padding center-content"
  title="testing"
>
  <a
    aria-label="testing"
    href="https://www.google.com"
  >
    <span
      className="bp3-icon"
    >
      <div
        height={22}
        src="/v4/svgs/mail.svg"
        width="22px"
      />
    </span>
  </a>
  <style />
</div>
`;

exports[`Navigation links All the links render as expected with a url 2`] = `
[
  <a
    className="center-content"
    href="https://www.google.com"
  >
    <span
      className="bp3-icon"
    >
      <div
        height={22}
        src="/v4/svgs/whiteLabel.svg"
        width="22px"
      />
    </span>
    testing
  </a>,
  <style />,
]
`;

exports[`Navigation links All the links render as expected with a url 3`] = `
[
  <a
    className="center-content"
    href="https://www.google.com"
  >
    <span
      className="bp3-icon"
    >
      <div
        height={20}
        src="/v4/svgs/loginRound.svg"
        width="20px"
      />
    </span>
    testing
  </a>,
  <style />,
]
`;

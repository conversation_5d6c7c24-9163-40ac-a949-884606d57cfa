import React from 'react';
import _ from 'lodash';

import mockSiteConfig from '../../../../../__mocks__/siteConfig.json';

describe('Navigation links', () => {
    test('All the links render as expected with a route', () => {
        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.contactLink.route.href', 'http://localhost:3000/v4/corsoroute');
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.demoLink.route.href', 'http://localhost:3000/v4/corsoroute');
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.loginLink.route.href', 'http://localhost:3000/v4/corsoroute');

        jest.setMock('rf-data/siteConfig.json', siteConfigCopy);

        const {ContactLink} = require('../contactLink');
        const {DemoLink} = require('../demoLink');
        const {LoginLink} = require('../loginLink');

        const links = [ContactLink, DemoLink, LoginLink];

        for (const NavLink of links) {
            const component = render(<NavLink/>);

            expect(component.toJSON()).toMatchSnapshot();
        }
    });

    test('All the links render as expected with a url', () => {
        const props = {text: 'testing', url: 'https://www.google.com'};

        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.contactLink', props);
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.demoLink', props);
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.loginLink', props);

        jest.setMock('rf-data/siteConfig.json', siteConfigCopy);

        const {ContactLink} = require('../contactLink');
        const {DemoLink} = require('../demoLink');
        const {LoginLink} = require('../loginLink');

        const links = [ContactLink, DemoLink, LoginLink];

        for (const NavLink of links) {
            const component = render(<NavLink/>);

            expect(component.toJSON()).toMatchSnapshot();
        }
    });

    test('Links that depend on text do not render without it', () => {
        const props = {url: 'https://www.google.com'};

        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.demoLink', props);
        _.set(siteConfigCopy, 'i18n.de.mainNavigation.loginLink', props);

        jest.setMock('rf-data/siteConfig.json', siteConfigCopy);

        const {DemoLink} = require('../demoLink');
        const {LoginLink} = require('../loginLink');

        const links = [DemoLink, LoginLink];

        for (const NavLink of links) {
            const component = render(<NavLink/>);

            expect(component.toJSON()).toBeFalsy();
        }
    });
});


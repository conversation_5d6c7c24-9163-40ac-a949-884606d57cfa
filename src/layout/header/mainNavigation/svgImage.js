import React from 'react';
import PropTypes from 'prop-types';

import {getImageSource} from 'rf-components/image/helpers';
import {colors} from 'rf-styles';

import {topIconSize} from './constants';

export function SvgImage({icon, size = topIconSize, color = colors.darkGray}) {
    const url = getImageSource({image: icon.asset});

    return (<>
        <div className={'svg-icon'}/>
        <style jsx>{`
          .svg-icon {
            mask-image: url(${url});
            mask-size: 100%;
            -webkit-mask-repeat: no-repeat;
            mask-repeat: no-repeat;
            mask-position: center;
            background-color: ${color};
            min-width: ${size}px;
            min-height: ${size}px;
            width: ${size}px;
            height: ${size}px;
          }
        `}
        </style>
    </>
    );
}

SvgImage.propTypes = {
    icon:  PropTypes.object,
    size:  PropTypes.number,
    color: PropTypes.string
};

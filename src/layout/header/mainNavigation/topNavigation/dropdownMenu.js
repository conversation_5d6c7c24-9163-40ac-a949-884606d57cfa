import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {colors, vSpacing, hSpacing, screenSizes, fontWeights} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';
import {T} from 'rf-i18n';

import {DropdownMenuItem} from './dropdownMenuItem';
import {topIconSize} from '../constants';
import {SvgImage} from '../svgImage';

const styles = css`
  .dropdown {
    height: 100%;
    position: relative;
    cursor: pointer;
    padding: 0 5px;
  }

  .dropdown:hover, .dropdown:hover .h5:not(a),
  .dropdown:focus-within, .dropdown:focus-within .h5:not(a) {
    color: ${colors.lightBlue};
  }
  
  .dropdown:hover :global(.svg-icon) {
    background-color: ${colors.lightBlue};
  }

  @media (max-width: ${screenSizes.m}px) {
    .dropdown {
      padding-left: ${hSpacing.xxs * 2}px;
      padding-right: ${hSpacing.xxs * 2}px;
    }
  }

  .dropdown:hover .dropdown-content,
  .dropdown:focus-within .dropdown-content {
    max-height: 1000px;
    padding: 12px 0;
    box-shadow: 0 2px 2px 2px rgba(0, 0, 0, 0.15);
  }

  .dropdown-content {
    display: flex;
    overflow: hidden;
    max-height: 0;
    position: absolute;
    z-index: -1;
    top: ${vSpacing.xl}px;
    left: ${-hSpacing.sm - -hSpacing.xxs}px;
    transition: all 200ms ease-in-out;
    background-color: ${colors.white};
    border-radius: 2px;
    flex-direction: column;
    cursor: default;
  }

  .h5 {
    padding-bottom: ${vSpacing.xxs}px;
    padding-left: ${hSpacing.xs}px;
    padding-right: ${hSpacing.xxs}px;
    margin-bottom: 0;
    white-space: nowrap;
    font-size: 14px;
    font-weight: ${fontWeights.semiBold};
  }

  .see-all {
    font-size: 14px;
    line-height: 18px;
    color: ${colors.blue};
    padding: 18px ${hSpacing.xl}px 8px ${hSpacing.xl}px;
    text-decoration: none;
  }

  .see-all:hover {
    color: ${colors.lightBlue};
  }`;

export function DropdownMenu({icon, iconFallback, text, items, seeAllRef}) {
    if (_.isEmpty(items)) {
        return null;
    }

    const href = seeAllRef?.href;

    return (
        <div className={'rf-cell center-content dropdown'} tabIndex={0}>
            {icon ? <SvgImage icon={icon}/>
                : <FleetsterIcon icon={iconFallback} iconSize={topIconSize}/>}
            <p className={'h5'}>{text}</p>
            <FleetsterIcon icon={'caretDown'} iconSize={14}/>
            <div className={'dropdown-content'} tabIndex={0}>
                {items.map(item => <DropdownMenuItem key={item._key} {...item}/>)}
                {seeAllRef && <a href={href} className={'h5 see-all'}><T>menu.seeAll</T></a>}
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

DropdownMenu.propTypes = {
    text:         PropTypes.string,
    iconFallback: PropTypes.string,
    icon:         PropTypes.object,
    items:        PropTypes.array,
    seeAllRef:    PropTypes.object
};

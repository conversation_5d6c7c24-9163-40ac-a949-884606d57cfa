import React from 'react';
import _ from 'lodash';

import mockSiteConfig from '../../../../../../__mocks__/siteConfig.json';

describe('Top navigation', () => {
    test('renders as expected', () => {
        const {TopNavigation} = require('../index');
        const component = render(<TopNavigation/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('Do not throw when dropdown is empty', () => {
        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.unset(siteConfigCopy, 'i18n.de.mainNavigation.dropdown');

        jest.doMock('rf-data/siteConfig.json', () => siteConfigCopy);

        const {TopNavigation} = require('../index');

        expect(() => render(<TopNavigation/>)).not.toThrow();
    });

    test('Renders fine if no links', () => {
        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.unset(siteConfigCopy, 'i18n.de.mainNavigation.links');

        jest.doMock('rf-data/siteConfig.json', () => siteConfigCopy);

        const {TopNavigation} = require('../index');

        const component = render(<TopNavigation/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});


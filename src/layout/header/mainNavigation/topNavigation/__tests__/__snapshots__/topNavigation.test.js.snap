// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Top navigation Renders fine if no links 1`] = `
<div
  className="center-content top-navigation"
>
  <div
    className="rf-cell center-content dropdown"
    tabIndex={0}
  >
    <span
      className="bp3-icon"
    >
      <div
        height={18}
        src="/v4/svgs/vehicle.svg"
        width="18px"
      />
    </span>
    <p
      className="h5"
    >
      Products
    </p>
    <span
      className="bp3-icon"
    >
      <div
        height={14}
        src="/v4/svgs/caretDown.svg"
        width="14px"
      />
    </span>
    <div
      className="dropdown-content"
      tabIndex={0}
    >
      <a
        className="nav-link"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          className="icon"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="undefined icon"
              decoding="async"
              height={450}
              loading="lazy"
              sizes="(max-width: 362px) 361px, 450px"
              src="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=361&h=361&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format 450w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 450,
                  "maxWidth": 450,
                  "width": "100%",
                }
              }
              width={450}
            />
          </div>
        </div>
        <div>
          <span
            className="h5"
          >
            Testing
          </span>
          <p />
        </div>
        <style />
      </a>
      <style />
      <a
        className="nav-link"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          className="icon"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="undefined icon"
              decoding="async"
              height={669}
              loading="lazy"
              sizes="(max-width: 362px) 361px, 604px"
              src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 669,
                  "maxWidth": 604,
                  "width": "100%",
                }
              }
              width={604}
            />
          </div>
        </div>
        <div>
          <span
            className="h5"
          >
            This is an even bigger text, let's see how this one behaves. Hope it works.
          </span>
          <p />
        </div>
        <style />
      </a>
      <style />
      <a
        className="nav-link"
        href="/ho-ho-ho"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          className="icon"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="undefined icon"
              decoding="async"
              height={669}
              loading="lazy"
              sizes="(max-width: 362px) 361px, 604px"
              src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 669,
                  "maxWidth": 604,
                  "width": "100%",
                }
              }
              width={604}
            />
          </div>
        </div>
        <div>
          <span
            className="h5"
          >
            meh
          </span>
          <p />
        </div>
        <style />
      </a>
      <style />
      <a
        className="h5 see-all"
      >
        menu.seeAll
      </a>
    </div>
    <style />
  </div>
  <style />
</div>
`;

exports[`Top navigation renders as expected 1`] = `
<div
  className="center-content top-navigation"
>
  <div
    className="rf-cell center-content dropdown"
    tabIndex={0}
  >
    <span
      className="bp3-icon"
    >
      <div
        height={18}
        src="/v4/svgs/vehicle.svg"
        width="18px"
      />
    </span>
    <p
      className="h5"
    >
      Products
    </p>
    <span
      className="bp3-icon"
    >
      <div
        height={14}
        src="/v4/svgs/caretDown.svg"
        width="14px"
      />
    </span>
    <div
      className="dropdown-content"
      tabIndex={0}
    >
      <a
        className="nav-link"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          className="icon"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="undefined icon"
              decoding="async"
              height={450}
              loading="lazy"
              sizes="(max-width: 362px) 361px, 450px"
              src="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=361&h=361&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format 450w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 450,
                  "maxWidth": 450,
                  "width": "100%",
                }
              }
              width={450}
            />
          </div>
        </div>
        <div>
          <span
            className="h5"
          >
            Testing
          </span>
          <p />
        </div>
        <style />
      </a>
      <style />
      <a
        className="nav-link"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          className="icon"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="undefined icon"
              decoding="async"
              height={669}
              loading="lazy"
              sizes="(max-width: 362px) 361px, 604px"
              src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 669,
                  "maxWidth": 604,
                  "width": "100%",
                }
              }
              width={604}
            />
          </div>
        </div>
        <div>
          <span
            className="h5"
          >
            This is an even bigger text, let's see how this one behaves. Hope it works.
          </span>
          <p />
        </div>
        <style />
      </a>
      <style />
      <a
        className="nav-link"
        href="/ho-ho-ho"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          className="icon"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="undefined icon"
              decoding="async"
              height={669}
              loading="lazy"
              sizes="(max-width: 362px) 361px, 604px"
              src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 669,
                  "maxWidth": 604,
                  "width": "100%",
                }
              }
              width={604}
            />
          </div>
        </div>
        <div>
          <span
            className="h5"
          >
            meh
          </span>
          <p />
        </div>
        <style />
      </a>
      <style />
      <a
        className="h5 see-all"
      >
        menu.seeAll
      </a>
    </div>
    <style />
  </div>
  <a
    className="rf-cell center-content nav-link"
    rel="noreferrer noopener"
    target="_blank"
  >
    <div
      style={
        {
          "minHeight": 18,
          "minWidth": 18,
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="Lejla test icon"
          decoding="async"
          height={18}
          loading="lazy"
          sizes="18px"
          src="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=9,0,449,449&w=18&h=18&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=9,0,449,449&w=18&h=18&auto=format 18w"
          style={
            {
              "height": "auto",
              "maxHeight": 18,
              "maxWidth": 18,
              "width": "100%",
            }
          }
          width={18}
        />
      </div>
    </div>
    <span
      className="h5"
    >
      Lejla test
    </span>
    <style />
  </a>
  <style />
  <a
    className="rf-cell center-content nav-link"
    href="https://www.google.com"
    rel="noreferrer noopener"
    target="_blank"
  >
    <div
      style={
        {
          "minHeight": 18,
          "minWidth": 18,
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="Google icon"
          decoding="async"
          height={18}
          loading="lazy"
          sizes="18px"
          src="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?rect=2,0,224,224&w=18&h=18&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?rect=2,0,224,224&w=18&h=18&auto=format 18w"
          style={
            {
              "height": "auto",
              "maxHeight": 18,
              "maxWidth": 18,
              "width": "100%",
            }
          }
          width={18}
        />
      </div>
    </div>
    <span
      className="h5"
    >
      Google
    </span>
    <style />
  </a>
  <style />
  <style />
</div>
`;

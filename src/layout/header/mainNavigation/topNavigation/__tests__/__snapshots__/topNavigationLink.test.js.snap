// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Top navigation link renders as expected with a route 1`] = `
[
  <a
    className="rf-cell center-content nav-link"
    href="http://localhost:3000/v4/corsoroute"
  >
    <div
      style={
        {
          "minHeight": 18,
          "minWidth": 18,
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="testing icon"
          decoding="async"
          height={18}
          loading="lazy"
          sizes="18px"
          src="https://cdn.sanity.io/images/dp11egz7/development/b56718596084267a4f79129c51096493d1bdce72-225x225.png?w=18&h=18&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/b56718596084267a4f79129c51096493d1bdce72-225x225.png?w=18&h=18&auto=format 18w"
          style={
            {
              "height": "auto",
              "maxHeight": 18,
              "maxWidth": 18,
              "width": "100%",
            }
          }
          width={18}
        />
      </div>
    </div>
    <span
      className="h5"
    >
      testing
    </span>
    <style />
  </a>,
  <style />,
]
`;

exports[`Top navigation link renders as expected with a url 1`] = `
[
  <a
    className="rf-cell center-content nav-link"
    href="https://www.google.com"
    rel="noreferrer noopener"
    target="_blank"
  >
    <div
      style={
        {
          "minHeight": 18,
          "minWidth": 18,
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="testing icon"
          decoding="async"
          height={18}
          loading="lazy"
          sizes="18px"
          src="https://cdn.sanity.io/images/dp11egz7/development/b56718596084267a4f79129c51096493d1bdce72-225x225.png?w=18&h=18&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/b56718596084267a4f79129c51096493d1bdce72-225x225.png?w=18&h=18&auto=format 18w"
          style={
            {
              "height": "auto",
              "maxHeight": 18,
              "maxWidth": 18,
              "width": "100%",
            }
          }
          width={18}
        />
      </div>
    </div>
    <span
      className="h5"
    >
      testing
    </span>
    <style />
  </a>,
  <style />,
]
`;

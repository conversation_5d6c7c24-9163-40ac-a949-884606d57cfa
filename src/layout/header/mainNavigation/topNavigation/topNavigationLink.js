import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {Image, RFLink} from 'rf-components';
import {vSpacing, hSpacing, screenSizes, colors} from 'rf-styles';

import {topIconSize} from '../constants';

const styles = css`
.nav-link {
    white-space: nowrap;
    height: 100%;
    margin-bottom: 0;
    text-decoration: none;
    outline: none;
}
.nav-link:hover, .nav-link:focus {
    background-color: ${colors.lightGray};
}
@media (max-width: ${screenSizes.m}px) { .nav-link {
    padding-left: ${hSpacing.xs}px;
    padding-right: ${hSpacing.xs}px;
}}

.h5 {
    font-size: 14px;
    padding-bottom: ${vSpacing.xxs}px;
    padding-left: ${hSpacing.xxs}px;
    margin-bottom: 0;
}`;

export function TopNavigationLink({icon, link, _key}) {
    return (
        <RFLink className={'rf-cell center-content nav-link'} route={link.route} url={link.url} styles={styles} key={_key} >
            <div style={{minWidth: topIconSize, minHeight: topIconSize}}>
                <Image image={icon} width={topIconSize} height={topIconSize} imageAlt={`${link.text} icon`}/>
            </div>
            <span className={'h5'}>{link.text}</span>
            <style jsx>{styles}</style>
        </RFLink>
    );
}

TopNavigationLink.propTypes = {
    icon: PropTypes.object,
    link: PropTypes.object,
    _key: PropTypes.string
};


import React from 'react';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {colors, vSpacing, hSpacing} from 'rf-styles';

import {SideNavigationLink} from './sideNavigationLink';
import {DropdownLinks} from './dropdownLinks';
import {DemoLink} from '../demoLink';
import {LoginLink} from '../loginLink';
import {FleetsterIcon} from 'rf-icon';
import {SideNavigationContactLink} from './sideNavigationContactLink';

const SLIDER_XS_WIDTH = 230 - (hSpacing.sm * 2);

const styles = css`
  .sider {
    display: flex;
    flex-direction: column;
    background-color: ${colors.white};
    width: ${SLIDER_XS_WIDTH}px;
    height: 100%;
    overflow: auto;
    box-shadow: 2px 0 4px 0 rgba(0, 0, 0, 0.24);
    padding: 0 ${hSpacing.sm}px;
  }

  .bottom-buttons {
    margin-top: ${vSpacing.s / 2}px;
    margin-bottom: ${vSpacing.s}px;
    display: flex;
    column-gap: ${hSpacing.xs}px;
  }

  .toggle-container {
    min-height: 65px;
    display: flex;
    width: fit-content;
    align-items: center;
  }
`;

export function Sider() {
    const {dropdown = {}, links = [], dropdownResources} = _.get(i18n, [language, 'mainNavigation'], {});

    return (
        <div className={'sider'}>
            <label className={'toggle-container'} htmlFor={'sider-toggle'}>
                <FleetsterIcon customStyles={{cursor: 'pointer'}} icon={'hamburguerList'}/>
            </label>
            <DropdownLinks iconFallback={'vehicle'} {...dropdown}/>
            {links.map(({_key, link, icon}) => (<SideNavigationLink
                key={_key}
                text={link.text}
                route={link.route}
                link={link.url}
                icon={icon}
                showIcon
            />))}
            <DropdownLinks iconFallback={'resourcesBox'} {...dropdownResources}/>
            <SideNavigationContactLink/>
            <div style={{flex: 100}}/>
            <div className={'bottom-buttons'}>
                <DemoLink/>
                <LoginLink/>
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

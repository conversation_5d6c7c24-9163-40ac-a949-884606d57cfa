import React from 'react';
import _ from 'lodash';

import mockSiteConfig from '../../../../../../__mocks__/siteConfig.json';
import {SideNavigationContactLink} from '../sideNavigationContactLink';

describe('Side navigation contact link', () => {
    test('renders as a expected', () => {
        const component = render(<SideNavigationContactLink/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('returns null when contact link data not available', () => {
        let siteConfigCopy = _.cloneDeep(mockSiteConfig);
        _.unset(siteConfigCopy, 'i18n.de.mainNavigation.contactLink');

        jest.setMock('rf-data/siteConfig.json', siteConfigCopy);

        const {SideNavigationContactLink} = require('../sideNavigationContactLink');

        const component = render(<SideNavigationContactLink/>);

        expect(component.toJSON()).toBeNull();
    });
});

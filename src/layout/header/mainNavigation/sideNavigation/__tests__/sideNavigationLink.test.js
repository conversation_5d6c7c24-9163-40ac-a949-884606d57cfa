import React from 'react';

describe('Side navigation link', () => {
    const icon = {
        _type: 'image',
        asset: {
            _ref:  'image-b56718596084267a4f79129c51096493d1bdce72-225x225-png',
            _type: 'reference'
        }
    };

    test('renders as expected with a route', () => {
        const {SideNavigationLink} = require('../sideNavigationLink');

        const component = render(<SideNavigationLink text={'testing'} route={{href: 'http://localhost:3000/v4/corsoroute'}}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders as expected with a url', () => {
        const {SideNavigationLink} = require('../sideNavigationLink');

        const component = render(<SideNavigationLink text={'testing'} link={'https://www.google.com'}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders nothing with no text', () => {
        const {SideNavigationLink} = require('../sideNavigationLink');

        const component = render(<SideNavigationLink link={'https://www.google.com'}/>);

        expect(component.toJSON()).toBeFalsy();
    });

    test('renders icon if showIcon is true', () => {
        const {SideNavigationLink} = require('../sideNavigationLink');

        const component = render(<SideNavigationLink text={'testing'} route={{_ref: 'page-that-doesnt-exist'}} icon={icon} showIcon={true}/>);

        expect(component.root.findAllByType('img')).toHaveLength(1);
    });

    test('renders no icon if showIcon is falsy', () => {
        const {SideNavigationLink} = require('../sideNavigationLink');

        const component = render(<SideNavigationLink text={'testing'} route={{_ref: 'page-that-doesnt-exist'}} icon={icon}/>);

        expect(component.root.findAllByType('img')).toHaveLength(0);
    });
});


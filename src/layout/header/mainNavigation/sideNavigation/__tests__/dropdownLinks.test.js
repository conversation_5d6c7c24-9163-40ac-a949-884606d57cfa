import React from 'react';

import {DropdownLinks} from '../dropdownLinks';

describe('Dropdown links', () => {
    test('renders as expected', () => {
        const component = render(<DropdownLinks text={'text'} items={[{text: '1', link: 'some-link'}]}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('returns null if items are empty', () => {
        const component = render(<DropdownLinks text={'text'} items={[]}/>);

        expect(component.toJSON()).toBeNull();
    });

    test('renders links when expanded', () => {
        const component = render(<DropdownLinks text={'text'} items={[{text: '1', link: 'some-link'}]}/>);

        expect(component.root.findAllByProps({className: 'dropdown-container'})).toHaveLength(0);

        act(() => {
            component.root.findByProps({className: 'dropdown-trigger'}).props.onClick();
        });

        expect(component.root.findByProps({className: 'dropdown-container'})).toBeDefined();
    });

    test('seeAllRef returns the see all link', () => {
        const component = render(<DropdownLinks seeAllRef={{href: 'some-href'}} text={'text'} items={[{text: '1', link: 'some-link'}]}/>);

        act(() => {
            component.root.findByProps({className: 'dropdown-trigger'}).props.onClick();
        });

        expect(component.root.findByProps({className: 'h5 see-all'})).toBeDefined();
    });
});


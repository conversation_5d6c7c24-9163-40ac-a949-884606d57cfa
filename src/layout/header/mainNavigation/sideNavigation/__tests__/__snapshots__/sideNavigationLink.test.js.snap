// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Side navigation link renders as expected with a route 1`] = `
[
  <a
    className="nav-link"
    href="http://localhost:3000/v4/corsoroute"
  >
    <span
      className="h5"
    >
      testing
    </span>
    <style />
  </a>,
  <style />,
]
`;

exports[`Side navigation link renders as expected with a url 1`] = `
[
  <a
    className="nav-link"
    href="https://www.google.com"
    rel="noreferrer noopener"
    target="_blank"
  >
    <span
      className="h5"
    >
      testing
    </span>
    <style />
  </a>,
  <style />,
]
`;

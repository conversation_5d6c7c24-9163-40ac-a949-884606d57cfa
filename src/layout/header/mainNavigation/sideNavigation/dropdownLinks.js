import React, {useState} from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {colors, hSpacing, fontWeights} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';
import {T} from 'rf-i18n';

import {SideNavigationLink} from './sideNavigationLink';
import {siderIconSize} from '../constants';
import {SvgImage} from '../svgImage';

const styles = css`
    .see-all {
        display: block;
        color: ${colors.blue};
        padding: ${hSpacing.xs}px;
        text-decoration: none;
        margin-bottom: 0;
        margin-left: ${siderIconSize + 6}px;
        user-select: none;
    }
  
    .see-all:hover, .see-all:focus {
        color: ${colors.lightBlue};
    }
  
    .h5 {
      padding: ${hSpacing.xs}px;
      margin-bottom: 0;
      white-space: nowrap;
      font-size: 12px;
      line-height: 18px;
      font-weight: ${fontWeights.semiBold};
    }
  
    .dropdown-trigger {
      display: flex;
      cursor: pointer;
      align-items: center;
      user-select: none;
      padding-left: 5px;
    }
  
    .dropdown-trigger:hover {
      background-color: ${colors.lightGray};
    }
`;

export function DropdownLinks({icon, iconFallback, text, items = [], seeAllRef = {}}) {
    const href = seeAllRef?.href;
    const [expand, setExpand] = useState(false);

    if (_.isEmpty(items)) {
        return null;
    }

    return (
        <div>
            <div className={'dropdown-trigger'} onClick={() => setExpand(expand => !expand)}>
                {icon ? <SvgImage icon={icon}/>
                    : <FleetsterIcon icon={iconFallback} iconSize={siderIconSize}/>}
                <p className={'h5'}>{text}</p>
                <FleetsterIcon icon={'caretDown'} iconSize={14}/>
            </div>
            {expand && <div className={'dropdown-container'}>
                {items.map(item => <SideNavigationLink key={item._key} link={item.url} {...item} showIcon/>)}
                {seeAllRef && href
                    ? <a href={href} className={'h5 see-all'}><T>menu.seeAll</T></a>
                    : ''}
            </div>}
            <style jsx>{styles}</style>
        </div>
    );
}

DropdownLinks.propTypes = {
    icon:         PropTypes.object,
    iconFallback: PropTypes.string,
    text:         PropTypes.string,
    items:        PropTypes.array,
    seeAllRef:    PropTypes.object
};

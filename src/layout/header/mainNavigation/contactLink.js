import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language} from 'rf-data/siteConfig.json';
import {removeEndSlash} from 'rf-utils';
import {FleetsterIcon} from 'rf-icon';
import {colors, screenSizes} from 'rf-styles';

const styles = css`
a {
    color: ${colors.darkGray};
    margin-bottom: 0;
    display: none;
}
a:hover, a:focus-within {
    color: ${colors.gray};
}

@media (min-width: ${screenSizes.s}px) { a {
    display: block
}}
`;

export function ContactLink() {
    const {text, route, url} = _.get(i18n, [language, 'mainNavigation', 'contactLink'], {});

    const href = _.get(route, 'href', removeEndSlash(url));

    return (
        <div className={'icon-padding center-content'} title={text}>
            <a href={href} aria-label={text}><FleetsterIcon iconSize={22} icon={'mail'}/></a>
            <style jsx>{styles}</style>
        </div>
    );
}

ContactLink.propTypes = {
    text:  PropTypes.string,
    route: PropTypes.object,
    url:   PropTypes.string
};


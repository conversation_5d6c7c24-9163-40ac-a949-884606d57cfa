import React from 'react';
import _ from 'lodash';

import {LanguageLink} from './languageLink';

export function getAltLanguageLinks({route, languages, configLang}) {
    return _.compact(_.map(languages, language => {
        const languageLabel = _.toLower(language.label);
        const slug = _.get(route, `i18n.${languageLabel}.slug.current`);

        if (_.isUndefined(slug) || languageLabel === configLang) {
            return null;
        }

        const href = `${language.href}/${slug}`;

        return <LanguageLink href={href} lang={language} key={language.label}/>;
    }));
}

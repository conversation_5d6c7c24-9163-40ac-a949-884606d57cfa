// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PERSON><PERSON> renders as expected 1`] = `
<header
  className="header-component"
>
  <div
    className="page-horizontal-padding page-horizontal-padding center-content header-container"
  >
    <div
      className="side-navigation"
    >
      <div
        className="sider-button"
      >
        <label
          htmlFor="sider-toggle"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/hamburguerList.svg"
              width="20px"
            />
          </span>
        </label>
        <input
          id="sider-toggle"
          type="checkbox"
        />
        <div
          className="sider-container"
        >
          <label
            htmlFor="sider-toggle"
          />
          <div
            className="sider"
          >
            <label
              className="toggle-container"
              htmlFor="sider-toggle"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={20}
                  src="/v4/svgs/hamburguerList.svg"
                  width="20px"
                />
              </span>
            </label>
            <div>
              <div
                className="dropdown-trigger"
                onClick={[Function]}
              >
                <span
                  className="bp3-icon"
                >
                  <div
                    height={18}
                    src="/v4/svgs/vehicle.svg"
                    width="18px"
                  />
                </span>
                <p
                  className="h5"
                >
                  Products
                </p>
                <span
                  className="bp3-icon"
                >
                  <div
                    height={14}
                    src="/v4/svgs/caretDown.svg"
                    width="14px"
                  />
                </span>
              </div>
              <style />
            </div>
            <a
              className="nav-link"
              rel="noreferrer noopener"
              target="_blank"
            >
              <div
                className="icon"
              >
                <div
                  className="center-content"
                  style={
                    {
                      "height": "100%",
                      "width": "100%",
                    }
                  }
                >
                  <img
                    alt="Lejla test icon"
                    decoding="async"
                    height={449}
                    loading="lazy"
                    sizes="(max-width: 362px) 361px, 465px"
                    src="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?w=465&h=449&auto=format"
                    srcSet="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=1,0,464,449&w=361&h=349&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?w=465&h=449&auto=format 465w"
                    style={
                      {
                        "height": "auto",
                        "maxHeight": 449,
                        "maxWidth": 465,
                        "width": "100%",
                      }
                    }
                    width={465}
                  />
                </div>
              </div>
              <span
                className="h5"
              >
                Lejla test
              </span>
              <style />
            </a>
            <style />
            <a
              className="nav-link"
              href="https://www.google.com"
              rel="noreferrer noopener"
              target="_blank"
            >
              <div
                className="icon"
              >
                <div
                  className="center-content"
                  style={
                    {
                      "height": "100%",
                      "width": "100%",
                    }
                  }
                >
                  <img
                    alt="Google icon"
                    decoding="async"
                    height={224}
                    loading="lazy"
                    sizes="228px"
                    src="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?w=228&h=224&auto=format"
                    srcSet="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?w=228&h=224&auto=format 228w"
                    style={
                      {
                        "height": "auto",
                        "maxHeight": 224,
                        "maxWidth": 228,
                        "width": "100%",
                      }
                    }
                    width={228}
                  />
                </div>
              </div>
              <span
                className="h5"
              >
                Google
              </span>
              <style />
            </a>
            <style />
            <a
              aria-label="Contact us"
              className="nav-link"
            >
              <span
                className="bp3-icon"
              >
                <div
                  height={18}
                  src="/v4/svgs/mail.svg"
                  width="18px"
                />
              </span>
              <span
                className="h5"
              >
                Contact us
              </span>
            </a>
            <style />
            <div
              style={
                {
                  "flex": 100,
                }
              }
            />
            <div
              className="bottom-buttons"
            >
              <a
                className="center-content"
              >
                <span
                  className="bp3-icon"
                >
                  <div
                    height={22}
                    src="/v4/svgs/whiteLabel.svg"
                    width="22px"
                  />
                </span>
                Demo
              </a>
              <style />
              <a
                className="center-content"
                href="https://my.fleetster.net"
              >
                <span
                  className="bp3-icon"
                >
                  <div
                    height={20}
                    src="/v4/svgs/loginRound.svg"
                    width="20px"
                  />
                </span>
                Login
              </a>
              <style />
            </div>
            <style />
          </div>
        </div>
      </div>
      <style />
    </div>
    <a
      className="logo"
      href="http://localhost:3000"
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="fleetster"
          decoding="async"
          height={30}
          loading="lazy"
          sizes="170px"
          src="https://cdn.sanity.io/images/dp11egz7/development/f834f95f9016aa4c4419ac82f3ffe81509d7a115-1990x493.png?rect=0,72,1990,351&w=170&h=30&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/f834f95f9016aa4c4419ac82f3ffe81509d7a115-1990x493.png?rect=0,72,1990,351&w=170&h=30&auto=format 170w"
          style={
            {
              "height": "auto",
              "maxHeight": 30,
              "maxWidth": 170,
              "width": "100%",
            }
          }
          width={170}
        />
      </div>
      <style />
    </a>
    <div
      className="center-content top-navigation"
    >
      <div
        className="rf-cell center-content dropdown"
        tabIndex={0}
      >
        <span
          className="bp3-icon"
        >
          <div
            height={18}
            src="/v4/svgs/vehicle.svg"
            width="18px"
          />
        </span>
        <p
          className="h5"
        >
          Products
        </p>
        <span
          className="bp3-icon"
        >
          <div
            height={14}
            src="/v4/svgs/caretDown.svg"
            width="14px"
          />
        </span>
        <div
          className="dropdown-content"
          tabIndex={0}
        >
          <a
            className="nav-link"
            rel="noreferrer noopener"
            target="_blank"
          >
            <div
              className="icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={450}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, 450px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=361&h=361&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/4119c893040916f82e6d1304b78dca28eb231aee-450x450.png?w=450&h=450&auto=format 450w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 450,
                      "maxWidth": 450,
                      "width": "100%",
                    }
                  }
                  width={450}
                />
              </div>
            </div>
            <div>
              <span
                className="h5"
              >
                Testing
              </span>
              <p />
            </div>
            <style />
          </a>
          <style />
          <a
            className="nav-link"
            rel="noreferrer noopener"
            target="_blank"
          >
            <div
              className="icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={669}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, 604px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 669,
                      "maxWidth": 604,
                      "width": "100%",
                    }
                  }
                  width={604}
                />
              </div>
            </div>
            <div>
              <span
                className="h5"
              >
                This is an even bigger text, let's see how this one behaves. Hope it works.
              </span>
              <p />
            </div>
            <style />
          </a>
          <style />
          <a
            className="nav-link"
            href="/ho-ho-ho"
            rel="noreferrer noopener"
            target="_blank"
          >
            <div
              className="icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={669}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, 604px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 669,
                      "maxWidth": 604,
                      "width": "100%",
                    }
                  }
                  width={604}
                />
              </div>
            </div>
            <div>
              <span
                className="h5"
              >
                meh
              </span>
              <p />
            </div>
            <style />
          </a>
          <style />
          <a
            className="h5 see-all"
          >
            menu.seeAll
          </a>
        </div>
        <style />
      </div>
      <a
        className="rf-cell center-content nav-link"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          style={
            {
              "minHeight": 18,
              "minWidth": 18,
            }
          }
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Lejla test icon"
              decoding="async"
              height={18}
              loading="lazy"
              sizes="18px"
              src="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=9,0,449,449&w=18&h=18&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/d58fe928015b2c9d27c175e3163e952d81f4530c-465x449.png?rect=9,0,449,449&w=18&h=18&auto=format 18w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 18,
                  "maxWidth": 18,
                  "width": "100%",
                }
              }
              width={18}
            />
          </div>
        </div>
        <span
          className="h5"
        >
          Lejla test
        </span>
        <style />
      </a>
      <style />
      <a
        className="rf-cell center-content nav-link"
        href="https://www.google.com"
        rel="noreferrer noopener"
        target="_blank"
      >
        <div
          style={
            {
              "minHeight": 18,
              "minWidth": 18,
            }
          }
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Google icon"
              decoding="async"
              height={18}
              loading="lazy"
              sizes="18px"
              src="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?rect=2,0,224,224&w=18&h=18&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/e18ed6815845cfa79edc623d7e7f6ef6c31dc3bb-228x224.png?rect=2,0,224,224&w=18&h=18&auto=format 18w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 18,
                  "maxWidth": 18,
                  "width": "100%",
                }
              }
              width={18}
            />
          </div>
        </div>
        <span
          className="h5"
        >
          Google
        </span>
        <style />
      </a>
      <style />
      <style />
    </div>
    <div
      className="rf-flex-2"
    />
    <div
      className="center-content right-menu"
    >
      <div
        className="icon-padding center-content"
        title="Contact us"
      >
        <a
          aria-label="Contact us"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={22}
              src="/v4/svgs/mail.svg"
              width="22px"
            />
          </span>
        </a>
        <style />
      </div>
      <div
        className="icon-padding center-content language-picker"
      >
        <div
          className="center-content"
          style={
            {
              "flex": 0,
              "marginLeft": "auto",
            }
          }
        >
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/world.svg"
              width="20px"
            />
          </span>
        </div>
        <div
          className="language-dropdown"
        >
          <a
            className="language-link"
            href="http://localhost:3000/sale"
            hrefLang="en"
          >
            EN
            <style />
          </a>
          <a
            className="language-link"
            href="http://localhost:3000/dutchsales"
            hrefLang="nl"
          >
            NL
            <style />
          </a>
          <a
            className="language-link"
            href="http://localhost:3000/spanishsales"
            hrefLang="es"
          >
            ES
            <style />
          </a>
        </div>
        <style />
      </div>
      <div
        className="rf-cell right-buttons center-content"
      >
        <a
          className="center-content"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={22}
              src="/v4/svgs/whiteLabel.svg"
              width="22px"
            />
          </span>
          Demo
        </a>
        <style />
        <a
          className="center-content"
          href="https://my.fleetster.net"
        >
          <span
            className="bp3-icon"
          >
            <div
              height={20}
              src="/v4/svgs/loginRound.svg"
              width="20px"
            />
          </span>
          Login
        </a>
        <style />
      </div>
    </div>
  </div>
  <style />
</header>
`;

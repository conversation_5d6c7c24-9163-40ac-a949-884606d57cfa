import {getAltLanguageLinks} from '../getAltLanguageLinks';

describe('getAltLanguageLinks', () => {
    test('returns null if no slug is defined', () => {
        const route = {i18n: {en: {slug: {current: undefined}}}};
        const languages = [{label: 'en', href: 'https://www.example.net'}];
        const configLang = 'en';

        const result = getAltLanguageLinks({route, languages, configLang});

        expect(result).toEqual([]);
    });

    test('returns null if slug is defined but lang is same as config lang', () => {
        const route = {i18n: {en: {slug: {current: 'something'}}}};
        const languages = [{label: 'en', href: 'https://www.example.net'}];
        const configLang = 'en';

        const result = getAltLanguageLinks({route, languages, configLang});

        expect(result).toEqual([]);
    });

    test('returns LanguageLink if slug is defined and lang is different from config lang', () => {
        const route = {i18n: {es: {slug: {current: 'something'}}}};
        const languages = [{label: 'en', href: 'https://www.example.com'}, {label: 'es', href: 'https://www.example.es'}];
        const configLang = 'en';

        const result = getAltLanguageLinks({route, languages, configLang});

        expect(result).toHaveLength(1);
    });
});

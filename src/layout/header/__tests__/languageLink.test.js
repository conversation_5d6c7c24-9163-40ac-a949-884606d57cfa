import React from 'react';

describe('LanguageLink', () => {
    beforeEach(() => {
        jest.doMock('next/head', () => props => <div {...props} />);
    });

    test('renders correctly', () => {
        const {LanguageLink} = require('../languageLink');

        const component = render(<LanguageLink href={'https://www.fleetster.de/etwas/deutsch'} lang={{label: 'DE'}}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});


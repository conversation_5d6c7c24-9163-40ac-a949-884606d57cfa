import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, vSpacing, hSpacing, screenSizes} from 'rf-styles';
import {FleetsterIcon} from 'rf-icon';
import {t} from 'rf-i18n';

import {SegmentLink} from './segmentLink';

const styles = css`
  .footer-segment {
    margin-left: 0;
    margin-top: 0;
    width: unset;
  }

  .segment-links {
    display: none;
  }

  .caret-down {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    cursor: pointer;
    position: absolute;
    width: 100%;
    height: calc(100% - ${vSpacing.s}px);
  }

  @media (min-width: ${screenSizes.xl}px) {
    .footer-segment {
      margin-left: 90px;
    }
  }

  @media (min-width: ${screenSizes.s}px) {
    .footer-segment {
      width: 30%;
      margin-left: ${hSpacing.s}px;
    }

    .segment-links {
      display: block;
    }

    .caret-down {
      display: none;
    }
  }
  
  .segment-title {
    display: flex;
    position: relative;
    padding-right: ${hSpacing.m}px
  }

  .h4 {
    font-size: 16px;
    line-height: 20px;
    color: ${colors.white};
    margin-bottom: ${vSpacing.s}px;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .h4 {
      font-size: 20px;
      line-height: 24px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .h4 {
      font-size: 24px;
      line-height: 28px;
    }
  }

  .section-toggle {
    display: none
  }

  .section-toggle:checked ~ .segment-links {
    display: block;
  }

  .section-toggle:checked ~ .segment-title .caret-down {
    transform: rotate(180deg);
    justify-content: flex-start;
  }`;

export function FooterSegment({_key, title, viewAllRef, links = []}) {
    const toggleId = `segment-toggle-${_key}`;

    return (
        <div className={'footer-segment'}>
            <input className={'section-toggle'} type={'checkbox'} id={toggleId}/>
            <div className={'segment-title'}>
                <h3 className={'h4'}>{title}</h3>
                <label htmlFor={toggleId} className={'caret-down'}>
                    <FleetsterIcon icon={'caretDown'} iconSize={14} customStyles={{color: colors.white}}/>
                </label>
            </div>
            <div className={'segment-links'}>
                {links.map(link => <SegmentLink key={link._key} {...link}/>)}
                {viewAllRef && <SegmentLink text={t('products.viewAll')} route={viewAllRef} viewAll={true}/>}
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

FooterSegment.propTypes = {
    _key:       PropTypes.string,
    title:      PropTypes.string,
    viewAllRef: PropTypes.object,
    links:      PropTypes.array
};

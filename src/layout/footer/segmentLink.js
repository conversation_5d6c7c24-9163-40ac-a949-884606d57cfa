import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, vSpacing, screenSizes, fontWeights} from 'rf-styles';
import {removeEndSlash} from 'rf-utils';
import _ from 'lodash';

const styles = css`
  .segment-link {
    margin-bottom: ${vSpacing.s}px;
  }

  a, span {
    color: ${colors.white};
    text-decoration: none;
    font-size: 12px;
    line-height: 16px;
    cursor: pointer;
    font-weight: ${fontWeights.semiBold};
  }

  @media (min-width: ${screenSizes.xxs}px) {
    a, span {
      font-size: 14px;
      line-height: 18px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    a, span {
      font-size: 16px;
      line-height: 18px;
    }
  }

  a:hover, span:hover {
    color: ${colors.lighterBlue};
  }

  .view-all {
    margin-bottom: ${vSpacing.s}px;
  }

  .view-all a {
    color: ${colors.lightBlue};
    margin-bottom: 0;
  }

  .view-all a:hover {
    color: ${colors.lighterBlue};
  }
`;

export function SegmentLink({text, route, url, viewAll, _key, _type}) {
    if (!text) {
        return null;
    }

    if (_type === 'actionButton') {
        return null;
    }

    const href = _.get(route, 'href', removeEndSlash(url));

    return (
        <div className={`segment-link ${viewAll ? 'view-all' : ''}`} key={_key}>
            <a href={href}>{text}</a>
            <style jsx>{styles}</style>
        </div>
    );
}

SegmentLink.propTypes = {
    _key:    PropTypes.string,
    _type:   PropTypes.string,
    text:    PropTypes.string,
    route:   PropTypes.object,
    url:     PropTypes.string,
    viewAll: PropTypes.bool,
    action:  PropTypes.string
};

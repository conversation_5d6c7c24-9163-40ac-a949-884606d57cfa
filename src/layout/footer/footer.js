import React from 'react';
import css from 'styled-jsx/css';

import {colors} from 'rf-styles';

import {FooterTopSection} from './footerTopSection';
import {FooterBottomSection} from './footerBottomSection';

const styles = css`
.footer { background-color: ${colors.footer}; }
hr {
    border-color: ${colors.white};
    border-top: 0;
    margin: 0;
}`;

export function Footer() {
    return (
        <footer className={'page-horizontal-padding footer'}>
            <FooterTopSection/>
            <hr/>
            <FooterBottomSection/>
            <style jsx>{styles}</style>
        </footer>
    );
}

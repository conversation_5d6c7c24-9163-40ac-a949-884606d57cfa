import React from 'react';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {i18n, language, socialMediaLinks} from 'rf-data/siteConfig.json';
import {colors, vSpacing, hSpacing, screenSizes} from 'rf-styles';
import {Image} from 'rf-components';

import {KnowledgeButton} from './knowledgeButton';

const styles = css`
  .footer-bottom {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    background-color: ${colors.footer};
    padding-bottom: ${vSpacing.s}px;
  }

  @media (min-width: ${screenSizes.xs}px) {
    .footer-bottom {
      flex-direction: row;
    }
  }

  .footer-bottom-element {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  
  .with-top-margin {
    margin-top: ${vSpacing.s}px;
  }

  .info {
    color: ${colors.white};
    text-decoration: none;
    line-height: ${vSpacing.m}px;
    margin-right: ${hSpacing.sm}px;
    margin-bottom: 0;
    white-space: nowrap;
  }

  .social-media-links {
    display: flex;
    flex-direction: row;
  }

  a {
    margin-right: ${hSpacing.sm}px;
    margin-bottom: 0;
  }

  a:hover {
    color: ${colors.lighterBlue};
  }`;

export function FooterBottomSection() {
    const {copyright} = _.get(i18n, [language, 'footer'], {});

    return (
        <div className={'footer-bottom'}>
            <div className={'footer-bottom-element with-top-margin'}>
                <span className={'info'}>
                    &copy; {new Date().getFullYear()} fleetster. {copyright}
                </span>
            </div>
            <div style={{display: 'flex', flex: 1, marginBottom: 0}}/>
            <div className={'footer-bottom-element'}>
                <div className={'social-media-links with-top-margin'}>
                    {socialMediaLinks.map(link => (<a key={link._key} href={link.url} title={link.title} target={'_blank'} rel={'noreferrer'}>
                        <Image image={link.icon} imageAlt={link.title}/>
                    </a>))}
                </div>
                <KnowledgeButton/>
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

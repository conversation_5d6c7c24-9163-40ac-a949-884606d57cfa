import React from 'react';

import moment from 'rf-moment-locale';
import siteConfig from 'rf-data/siteConfig.json';

import {Footer} from '../footer';

const languages = [
    'de',
    'en',
    'es',
    'nl',
    'notExisting'
];

describe('Footer', () => {
    const oldLanguage = siteConfig.language;

    beforeAll(() => {
        jest.spyOn(global.Date.prototype, 'getFullYear').mockImplementation(() => 2023);
    });

    afterEach(() => {
        siteConfig.language = oldLanguage;
    });

    test.each(languages)('renders as expected in %s', language => {
        siteConfig.language = language;

        const component = render(<Footer />);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders current year for copyright', () => {
        const component = render(<Footer/>);

        const rightsReservedInfoText = component.root.findAllByProps({className: 'info'})[0].children.join('');

        expect(rightsReservedInfoText).toMatch(`© ${moment().format('YYYY')} fleetster`);
    });
});

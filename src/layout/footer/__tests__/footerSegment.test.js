import React from 'react';
import _ from 'lodash';

describe('Footer segment', () => {
    const mockSegment = {
        _key:  'my_key',
        links: [{
            _key:  'my_key',
            _type: 'navigationLink',
            route: {_ref: 'page1', href: 'http://localhost:3000/v4/corsoroute'},
            text:  'Link 1'
        }],
        title:      'HueHueBr',
        viewAllRef: {_ref: 'page2', href: 'http://localhost:3000/v4/request/online-demo'}
    };

    test('renders as expected', () => {
        const {FooterSegment} = require('../footerSegment');

        const component = render(<FooterSegment {...mockSegment}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('Still renders if links list is non-existent', () => {
        const {FooterSegment} = require('../footerSegment');

        let mockSegmentCopy = _.cloneDeep(mockSegment);
        _.unset(mockSegmentCopy, 'links');

        const component = render(<FooterSegment {...mockSegmentCopy}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});


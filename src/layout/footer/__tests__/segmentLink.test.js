import React from 'react';

import {SegmentLink} from '../segmentLink';

describe('Footer segment link', () => {
    test('renders as expected with a route', () => {
        const component = render(<SegmentLink
            _type={'navigationLink'}
            text={'testing'}
            route={{href: 'http://localhost:3000/v4/corsoroute'}}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders as expected with a url', () => {
        const component = render(<SegmentLink
            _type={'navigationLink'}
            text={'testing'}
            link={'https://www.google.com'}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders nothing with no text', () => {
        const component = render(<SegmentLink _type={'navigationLink'} link={'https://www.google.com'}/>);

        expect(component.toJSON()).toBeFalsy();
    });

    test('renders a link without type', () => {
        const component = render(<SegmentLink text={'testing'} link={'https://www.google.com'}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('adds .view-all class when viewAll is true', () => {
        const component = render(<SegmentLink
            text={'testing'}
            link={'https://www.google.com'}
            viewAll/>);

        expect(component.root.children[0].props.className).toContain('view-all');
    });

    test('renders nothing with unknown action', () => {
        const component = render(<SegmentLink
            _type={'actionButton'}
            text={'testing'}
            action={'other'}
            link={'https://www.google.com'}/>);

        expect(component.toJSON()).toBeFalsy();
    });
});


import React from 'react';
import css from 'styled-jsx/css';

import {Image} from 'rf-components';
import {footerLogo} from 'rf-data/siteConfig.json';
import {colors, screenSizes, vSpacing, hSpacing} from 'rf-styles';

const styles = css`
  .footer-text {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .footer-logo {
    align-self: flex-start;
    margin-top: 0;
    margin-bottom: ${vSpacing.s}px;
  }

  p {
    font-size: 12px;
    line-height: 22px;
    margin-bottom: 0;
    color: ${colors.white};
    border-bottom: 1px solid ${colors.white};
    padding-bottom: 20px;
  }

  @media (min-width: ${screenSizes.s}px) {
    .footer-text {
      width: 20%;
      margin-right: ${hSpacing.m}px;
    }

    p {
      border: none;
    }

    .footer-segments {
      flex-wrap: wrap;
      flex-direction: row;
    }
  }
  @media (min-width: ${screenSizes.m}px) {
    .footer-text {
      width: 20%;
      margin-bottom: 0;
      margin-right: ${hSpacing.l}px;
    }

    p {
      font-size: 14px;
      line-height: 24px;
    }
  }
  @media (min-width: ${screenSizes.l}px) {
    .footer-segments {
      flex-wrap: nowrap;
      flex-direction: row;
    }
  }
  @media (min-width: ${screenSizes.l}px) {
    .footer-text {
      width: 20%;
    }

    p {
      font-size: 14px;
      line-height: 24px;
    }
  }
  @media (min-width: ${screenSizes.xl}px) {
    .footer-text {
      width: 18%;
    }
  }
`;

export function FooterTopSectionText({text}) {
    return (
        <div className={'footer-text'}>
            <h2 className={'footer-logo'}>
                <Image image={footerLogo} imageAlt={footerLogo.alt}/>
            </h2>
            <p>{text}</p>
            <style jsx>{styles}</style>
        </div>
    );
}

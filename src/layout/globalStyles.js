import 'normalize.css/normalize.css';
import {global} from 'styled-jsx/css';

import {screenSizes, vSpacing, hSpacing, sectionHorizontalPadding} from 'rf-styles';

import {openSansCss} from './openSansFont';

export const globalStyles = global`
${openSansCss}

section { 
    position: relative; 
}

.section-vertical-padding {
    padding: ${vSpacing.l}px 0;
}
@media (min-width: ${screenSizes.s}px) { .section-vertical-padding {
    padding: ${vSpacing.xl}px 0;
}}

.page-horizontal-padding {
    box-sizing: border-box;
    padding-left: ${sectionHorizontalPadding.xxs}px;
    padding-right: ${sectionHorizontalPadding.xxs}px;
}
@media (min-width: ${screenSizes.xxs}px) { .page-horizontal-padding {
    padding-left: ${sectionHorizontalPadding.xs}px;
    padding-right: ${sectionHorizontalPadding.xs}px;
}}
@media (min-width: ${screenSizes.xs}px) { .page-horizontal-padding {
    padding-left: ${sectionHorizontalPadding.s}px;
    padding-right: ${sectionHorizontalPadding.s}px;
}}
@media (min-width: ${screenSizes.s}px) { .page-horizontal-padding {
    padding-left: ${sectionHorizontalPadding.m}px;
    padding-right: ${sectionHorizontalPadding.m}px;
}}
@media (min-width: ${screenSizes.m}px) { .page-horizontal-padding {
    padding-left: ${sectionHorizontalPadding.l}px;
    padding-right: ${sectionHorizontalPadding.l}px;
}}
@media (min-width: ${screenSizes.l}px) { .page-horizontal-padding {
    padding-left: ${sectionHorizontalPadding.xl}px;
    padding-right: ${sectionHorizontalPadding.xl}px;
}}
@media (min-width: ${screenSizes.xl}px) { .page-horizontal-padding {
    padding-left: ${sectionHorizontalPadding.xxl}px;
    padding-right: ${sectionHorizontalPadding.xxl}px;
}}

.rf-flex-container {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    background-origin: border-box;
    padding: 0 ${hSpacing.xxs}px;
}
@media (min-width: ${screenSizes.xs}px) { .rf-flex-container {
    padding: 0 15px;
}}
@media (min-width: ${screenSizes.s}px) { .rf-flex-container {
    flex-direction: row;
}}

.rf-cell {
    padding: 0 ${hSpacing.xxs}px;
    flex: 1 1 auto;
}
@media (min-width: ${screenSizes.xs}px) { .rf-cell {
    padding: 0 ${hSpacing.s}px;
}}
@media (min-width: ${screenSizes.m}px) { .rf-cell {
    flex-basis: 0%;
}}

.rf-flex-1 { flex: 1 1 auto }
.rf-flex-2 { flex: 2 1 auto }
.rf-flex-3 { flex: 3 1 auto }
.rf-flex-4 { flex: 4 1 auto }
.rf-flex-5 { flex: 5 1 auto }
.rf-flex-6 { flex: 6 1 auto }
@media (min-width: ${screenSizes.m}px) { 
    .rf-flex-1, .rf-flex-2, .rf-flex-3, 
    .rf-flex-4, .rf-flex-5, .rf-flex-6 {
        flex-basis: 0%;
    }
}

.center-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-padding {
    padding: 0 ${hSpacing.xs}px;
}
`;

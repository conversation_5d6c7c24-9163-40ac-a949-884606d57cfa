import React, {useEffect} from 'react';
import PropTypes from 'prop-types';
import Head from 'next/head';
import css from 'styled-jsx/css';

import {colors, screenSizes, vSpacing} from 'rf-styles';

import {Header} from './header';
import {Footer} from './footer';
import {injectTagManagerScripts} from './tagManager/tagManagerScripts';
import {globalStyles} from './globalStyles';
import {typography} from './typography';
import {OrganizationInformation} from './organizationInformation';
import {BreadCrumbs} from './structuredData/breadCrumbs';
import {PreLoadFonts} from './preloadFonts';

const styles = css`
  .page-container {
    background-color: ${colors.white};
    min-width: ${screenSizes.xxs}px;
    overflow-x: hidden;
  }

  .main-content {
    padding-top: ${vSpacing.xl}px;
  }`;

export function Layout({children, route}) {
    const {breadcrumbsList, host, logo} = route;

    useEffect(() => {
        injectTagManagerScripts();
    }, []);

    return (
        <>
            <Head>
                <PreLoadFonts/>
                <link rel={'shortcut icon'} href={'/favicon.ico'}/>
                <link rel={'preconnect'} href={'https://cdn.sanity.io'}/>
            </Head>
            <OrganizationInformation host={host} logo={logo}/>
            <BreadCrumbs breadcrumbsList={breadcrumbsList}/>
            <div className={'page-container'}>
                <Header route={route}/>
                <main className={'main-content'}>{children}</main>
                <Footer/>
            </div>
            <style jsx>{styles}</style>
            <style jsx global>{globalStyles}</style>
            <style jsx global>{typography}</style>
        </>
    );
}

Layout.propTypes = {
    route:    PropTypes.object,
    children: PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.array
    ])
};

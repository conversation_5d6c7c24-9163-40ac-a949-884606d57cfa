import React from 'react';
import PropTypes from 'prop-types';
import * as _ from 'lodash';
import Head from 'next/head';

export function BreadCrumbs({breadcrumbsList}) {
    if (_.isEmpty(breadcrumbsList)) {
        return null;
    }

    const breadCrumbs = {
        '@context':      'https://schema.org',
        '@type':         'BreadcrumbList',
        itemListElement: breadcrumbsList
    };

    const structureDataHtml = {
        __html: JSON.stringify(breadCrumbs)
    };

    return (
        <Head>
            <script dangerouslySetInnerHTML={structureDataHtml} type={'application/ld+json'}/>
        </Head>
    );
}

BreadCrumbs.propTypes = {
    breadcrumbsList: PropTypes.array
};

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BreadCrumbs does not fail if there is no data 1`] = `null`;

exports[`BreadCrumbs renders correctly 1`] = `
<div>
  <script
    dangerouslySetInnerHTML={
      {
        "__html": "{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":{"@id":"https://www.fleetster.de/","name":"Software for Fleet Management, Car Sharing and Rental"}}]}",
      }
    }
    type="application/ld+json"
  />
</div>
`;

import React from 'react';

import {BreadCrumbs} from '../breadCrumbs';

// eslint-disable-next-line react/prop-types
jest.mock('next/head', () => ({children}) => <div>{children}</div>);

describe('BreadCrumbs', () => {
    it('renders correctly', () => {
        const itemList = [{
            '@type':  'ListItem',
            position: 1,
            item:     {'@id': 'https://www.fleetster.de/', name: 'Software for Fleet Management, Car Sharing and Rental'}
        }];

        const component = render(<BreadCrumbs breadcrumbsList={itemList}/>);

        expect(component).toMatchSnapshot();
    });

    it('does not fail if there is no data', () => {
        const component = render(<BreadCrumbs/>);

        expect(component).toMatchSnapshot();
    });
});

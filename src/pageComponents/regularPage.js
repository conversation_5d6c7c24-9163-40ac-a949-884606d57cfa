import React from 'react';
import * as _ from 'lodash';
import {NextSeo} from 'next-seo';
import PropTypes from 'prop-types';

import {language} from 'rf-data/siteConfig.json';

import {Layout} from '../layout';
import {Content} from '../content/content';
import {getLanguageAlternates} from './getLanguageAlternates';

export function RegularPage({route}) {
    const {canonical, disallowRobots, openGraph} = route;

    const page = _.get(route, `i18n.${language}.page.i18n.${language}`) || route.page;
    const {title = 'Missing title', description, content} = page;

    return (
        <Layout route={route}>
            <NextSeo
                title={title}
                description={description}
                canonical={canonical}
                openGraph={openGraph}
                noindex={disallowRobots}
                languageAlternates={getLanguageAlternates(route)}
            />
            <Content content={content}/>
        </Layout>
    );
}

RegularPage.propTypes = {
    route: PropTypes.object
};

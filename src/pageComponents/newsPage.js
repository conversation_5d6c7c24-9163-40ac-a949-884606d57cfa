import React from 'react';
import PropTypes from 'prop-types';
import {NextSeo} from 'next-seo';

import {PaginationSection} from 'rf-components';

import {getLanguageAlternates} from './getLanguageAlternates';
import {Layout} from '../layout';
import {NewsList} from '../content/news';

export function NewsPage({route, slug}) {
    return (
        <Layout route={route}>
            <NextSeo
                title={route.title}
                canonical={route.canonical}
                description={route.description}
                languageAlternates={getLanguageAlternates(route)}
            />
            {route && <NewsList news={route.content}/>}
            {slug &&
                <PaginationSection pageSlug={slug} pagination={route.pagination}
                    textFormatter={item => item.year}/>}
        </Layout>
    );
}

NewsPage.propTypes = {
    slug:  PropTypes.string,
    route: PropTypes.object
};

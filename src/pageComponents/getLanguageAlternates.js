import * as _ from 'lodash';

import {languages} from 'rf-data/siteConfig.json';

const DEFAULT_LANGUAGE = 'en';

export function getLanguageAlternates(route) {
    const alternates = _.compact(_.map(languages, language => {
        const hrefLang = _.toLower(language.label);
        const currentSlug = _.get(route, `i18n.${hrefLang}.slug.current`);

        if (_.isUndefined(currentSlug)) {
            return null;
        }

        const href = `${language.href}/${currentSlug}`;

        return {href, hrefLang};
    }));

    const defaultHref = _.find(alternates, alternate => alternate.hrefLang === DEFAULT_LANGUAGE);

    if (defaultHref) {
        alternates.push({href: defaultHref?.href, hrefLang: 'x-default'});
    }

    return alternates;
}

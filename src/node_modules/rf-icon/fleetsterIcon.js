import React from 'react';
import PropTypes from 'prop-types';
import * as _ from 'lodash';
import css from 'styled-jsx/css';

import * as svgs from './svgs';

const styles = css`
.rf-icon svg {
    fill: currentColor;
    display: block;
}`;

export function FleetsterIcon({icon, iconSize = 20, customStyles}) {
    // eslint-disable-next-line import/namespace
    const SVG = svgs[_.upperFirst(icon)];

    if (!SVG) {
        return null;
    }

    return (
        <span className={'rf-icon'} style={customStyles}>
            <SVG style={{width: iconSize, height: iconSize}}/>
            <style jsx>{styles}</style>
        </span>
    );
}

FleetsterIcon.propTypes = {
    icon:         PropTypes.string,
    iconSize:     PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    customStyles: PropTypes.object
};

describe('Unit tests: queryStringUtil', () => {
    let queryStringUtil;
    let originalLocation = window.location;
    let windowLocationGetterFn;
    let windowLocationSetterFn;

    beforeAll(() => {
        windowLocationGetterFn = jest.fn();
        windowLocationSetterFn = jest.fn();

        Object.defineProperty(window, 'location', {
            get:          windowLocationGetterFn,
            set:          windowLocationSetterFn,
            configurable: true
        });
    });

    beforeEach(() => {
        queryStringUtil = require('rf-utils').queryStringUtil;

        windowLocationGetterFn.mockClear();
        windowLocationSetterFn.mockClear();
    });

    describe('getFromUrl', () => {
        test('retrieves query parameters from window.location.href if no url is passed', () => {
            windowLocationGetterFn.mockImplementationOnce(() => {
                return new URL('http://localhost');
            });

            const result = queryStringUtil.getFromUrl();

            expect(result).toEqual({});

            expect(windowLocationGetterFn).toHaveBeenCalled();
        });

        test('retrieves query parameters from the passed url if it is passed', () => {
            const result = queryStringUtil.getFromUrl('http://localhost');

            expect(result).toEqual({});

            expect(windowLocationGetterFn).not.toHaveBeenCalled();
        });

        test('correctly handles existing query parameters on window.location.href', () => {
            windowLocationGetterFn.mockImplementationOnce(() => {
                return new URL('http://localhost?test=true');
            });

            const result = queryStringUtil.getFromUrl();

            expect(result).toEqual({
                test: 'true'
            });
        });

        test('correctly handles existing query parameters on the passed url', () => {
            const result = queryStringUtil.getFromUrl('http://localhost?test=1');

            expect(result).toMatchObject({test: '1'});
        });
    });

    describe('addToUrl', () => {
        test('correctly handles receiving no query parameters', () => {
            const testUrl = 'http://localhost';

            const result = queryStringUtil.addToUrl(testUrl);

            expect(result).toEqual(testUrl);
        });

        test('correctly handles query parameters', () => {
            const testUrl = 'http://localhost';

            const result = queryStringUtil.addToUrl(testUrl, {test: 1});

            expect(result).toEqual(`${testUrl}?test=1`);
        });
    });

    afterAll(() => {
        window.location = originalLocation;
    });
});

describe('Unit tests: navigationUtil', () => {
    const originalLocation = window.location;
    const windowLocationGetterFn = jest.fn();
    const windowLocationSetterFn = jest.fn();
    windowLocationGetterFn.mockImplementation(() => new URL('http://localhost'));

    Object.defineProperty(window, 'location', {
        get:          windowLocationGetterFn,
        set:          windowLocationSetterFn,
        configurable: true
    });
    const navigationUtil = require('rf-utils').navigationUtil;

    describe('navigateTo', () => {
        test('navigates correctly when called without query parameters', () => {
            const testURL = 'http://googe.lo.cu';

            navigationUtil.navigateTo(testURL);

            expect(windowLocationSetterFn).toHaveBeenLastCalledWith(testURL);
        });

        test('navigates correctly when called without existing query parameters', () => {
            const testURL = 'http://googe.lo.cu';
            const queryParams = {test: 1, test2: 2};
            const testURLWithQueryParameters = `${testURL}?test=1&test2=2`;

            navigationUtil.navigateTo(testURL, queryParams);

            expect(windowLocationSetterFn).toHaveBeenLastCalledWith(testURLWithQueryParameters);
        });

        test('navigates correctly when called with existing query parameters', () => {
            windowLocationGetterFn.mockImplementation(() => new URL('http://localhost?test3=1'));
            const testURL = 'http://googe.lo.cu';
            const queryParams = {test: 1, test2: 2};
            const testURLWithQueryParameters = `${testURL}?test3=1&test=1&test2=2`;

            navigationUtil.navigateTo(testURL, queryParams);

            expect(windowLocationSetterFn).toHaveBeenLastCalledWith(testURLWithQueryParameters);
        });

        test('navigates correctly when called with a target that contains query parameters', () => {
            windowLocationGetterFn.mockImplementation(() => new URL('http://localhost?test3=1'));
            const testURL = 'http://googe.lo.cu';
            const queryParams = {test3: 3};
            const testURLWithQueryParameters = `${testURL}?test3=3`;

            navigationUtil.navigateTo(testURL, queryParams);

            expect(windowLocationSetterFn).toHaveBeenLastCalledWith(testURLWithQueryParameters);
        });
    });

    afterAll(() => {
        window.location = originalLocation;
    });
});

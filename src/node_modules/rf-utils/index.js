import _ from 'lodash';
import * as qs from 'qs';

import {host} from '../rf-data/siteConfig.json';

function getFromUrl(url = '') {
    let targetUrl = url;

    if (!url && typeof window !== 'undefined') {
        targetUrl = window.location.href;
    }

    if (!/\?/.test(targetUrl)) {
        targetUrl = '';
    }

    const searchParams = targetUrl.replace(/^.*\?/gi, '');

    return qs.parse(searchParams);
}

function addToUrl(url, queryObject) {
    const search = `${qs.stringify(queryObject)}`;

    return `${url}${search ? '?' : ''}${search}`;
}

function encodeObjIntoString(obj) {
    return encodeURIComponent(JSON.stringify(obj));
}

function decodeObjFromString(str) {
    let result = null;
    try {
        result = JSON.parse(decodeURIComponent(str));
    } catch (e) {
        // ignored
    }

    return result;
}

export const queryStringUtil = {getFromUrl, addToUrl, encodeObjIntoString, decodeObjFromString};

function navigateTo(target, queryParams = {}) {
    const existingQueryParams = queryStringUtil.getFromUrl();
    const targetQueryParams = queryStringUtil.getFromUrl(target);

    _.merge(existingQueryParams, targetQueryParams, queryParams);

    const newUrl = queryStringUtil.addToUrl(target, existingQueryParams);

    window.location = newUrl;
}

export function removeEndSlash(url) {
    if (typeof url !== 'string') {
        return url;
    }

    return url
        .replace(/([^:])\/\//g, '$1/')
        .replace(/\/$/, '');
}

export const navigationUtil = {navigateTo};

export function mergeClassNames(defaultClasses, customClasses) {
    if (typeof defaultClasses === 'string') {
        defaultClasses = defaultClasses.split(' ');
    }

    if (typeof customClasses === 'string') {
        customClasses = customClasses.split(' ');
    }

    if (!customClasses) {
        return defaultClasses.join(' ');
    }

    return _.union(defaultClasses, customClasses).join(' ');
}

function isFileUrl(url = '') {
    const urlObject = new URL(url);

    return urlObject.pathname.split('.').length > 1;
}

export function shouldOpenInNewTab(url = '') {
    if (url.startsWith(host.url) && !isFileUrl(url)) {
        return false;
    }

    return true;
}

export function getAllLanguageSlugValues(values) {
    return _.mapValues(values, value => ({slug: {current: value}}));
}

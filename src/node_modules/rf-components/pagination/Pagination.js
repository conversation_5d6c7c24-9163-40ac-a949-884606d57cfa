import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {hSpacing, screenSizes, vSpacing} from 'rf-styles';

import {ArrowButton} from './ArrowButton';
import {ButtonList} from './ButtonList';

const styles = css`
  .pagination {
    margin-bottom: ${vSpacing.xl}px;
    padding: 0 ${hSpacing.sm}px;
  }

  @media (min-width: ${screenSizes.s}px) {
    .pagination {
      margin-top: ${vSpacing.s}px
    }
  }
`;

export function Pagination({
    pageSlug, showLeftArrow, showRightArrow, largeVisibleButtons, smallVisibleButtons,
    previousPage, nextPage, textFormatter
}) {
    return (
        <div className={'center-content pagination'}>
            <ArrowButton showArrow={showLeftArrow} pageRef={previousPage} arrowType={'arrowLeft'} arrowSize={'large'}/>
            <ArrowButton showArrow={showLeftArrow} pageRef={previousPage} arrowType={'arrowLeft'} arrowSize={'small'}/>
            <ButtonList pageSlug={pageSlug} buttons={largeVisibleButtons} size={'large'} formatter={textFormatter}/>
            <ButtonList pageSlug={pageSlug} buttons={smallVisibleButtons} size={'small'} formatter={textFormatter}/>
            <ArrowButton showArrow={showRightArrow} pageRef={nextPage} arrowType={'arrowRight'} arrowSize={'large'}/>
            <ArrowButton showArrow={showRightArrow} pageRef={nextPage} arrowType={'arrowRight'} arrowSize={'small'}/>
            <style jsx>{styles}</style>
        </div>
    );
}

Pagination.propTypes = {
    showLeftArrow:       PropTypes.bool,
    largeVisibleButtons: PropTypes.array,
    previousPage:        PropTypes.string,
    pageSlug:            PropTypes.string,
    textFormatter:       PropTypes.func,
    nextPage:            PropTypes.string,
    smallVisibleButtons: PropTypes.array,
    showRightArrow:      PropTypes.bool
};

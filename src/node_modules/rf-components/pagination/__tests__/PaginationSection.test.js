import React from 'react';

function testTextFormatter(item) {
    return item.year;
}

describe('PaginationSection', () => {
    const pagination = [
        {
            year:     2021,
            quarter:  1,
            pageSlug: 'product-updates/q1-2021'
        },
        {
            year:     2020,
            quarter:  4,
            pageSlug: 'product-updates/q4-2020'
        },
        {
            year:     2020,
            quarter:  3,
            pageSlug: 'product-updates/q3-2020'
        },
        {
            year:     2020,
            quarter:  2,
            pageSlug: 'product-updates/q2-2020'
        },
        {
            year:     2020,
            quarter:  1,
            pageSlug: 'product-updates/q1-2020'
        },
        {
            year:     2019,
            quarter:  2,
            pageSlug: 'product-updates/q2-2019'
        },
        {
            year:     2019,
            quarter:  1,
            pageSlug: 'product-updates/q1-2019'
        }
    ];

    test('renders index page pagination buttons', () => {
        let {PaginationSection} = require('../index');

        const component = render(<PaginationSection pageSlug={'product-updates'} pagination={pagination} textFormatter={testTextFormatter}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('selects specific quarter pagination button', () => {
        const {PaginationSection} = require('../index');

        const component = render(<PaginationSection pageSlug={'product-updates/q2-2020'} pagination={pagination} textFormatter={testTextFormatter}/>);

        const paginationButtonNodes = component.root.findAllByProps({href: 'http://localhost:3000/product-updates/q2-2020'});

        expect(paginationButtonNodes[0].props.className).toContain('selected');
        expect(paginationButtonNodes[1].props.className).toContain('selected');
    });

    test('still behaves when button index is greater than slice size', () => {
        const {PaginationSection} = require('../index');

        const component = render(<PaginationSection pageSlug={'product-updates/q1-2020'} pagination={pagination} textFormatter={testTextFormatter}/>);

        const largePaginationButtons = component.root
            .findByProps({className: 'pagination-buttons-large'})
            .findAllByType('a');

        expect(largePaginationButtons[2].props.className).toContain('selected');
    });

    test('if we are on the index page, shows no left arrow but shows right arrow', () => {
        let {PaginationSection} = require('../index');

        const component = render(<PaginationSection pageSlug={'product-updates'} pagination={pagination} textFormatter={testTextFormatter}/>);

        expect(component.root.findAllByProps({src: '/v4/svgs/arrowLeft.svg'})).toHaveLength(0);
        expect(component.root.findAllByProps({src: '/v4/svgs/arrowRight.svg'})).toHaveLength(2);
    });

    test('if selected button is first in list, shows no left arrow but shows right arrow', () => {
        const {PaginationSection} = require('../index');

        const component = render(<PaginationSection pageSlug={'product-updates/q1-2021'} pagination={pagination} textFormatter={testTextFormatter}/>);

        expect(component.root.findAllByProps({src: '/v4/svgs/arrowLeft.svg'})).toHaveLength(0);
        expect(component.root.findAllByProps({src: '/v4/svgs/arrowRight.svg'})).toHaveLength(2);
    });

    test('if selected button is last in list, shows no right arrow but shows left arrow', () => {
        const {PaginationSection} = require('../index');

        const component = render(<PaginationSection pageSlug={'product-updates/q1-2019'} pagination={pagination} textFormatter={testTextFormatter}/>);

        expect(component.root.findAllByProps({src: '/v4/svgs/arrowLeft.svg'})).toHaveLength(2);
        expect(component.root.findAllByProps({src: '/v4/svgs/arrowRight.svg'})).toHaveLength(0);
    });
});

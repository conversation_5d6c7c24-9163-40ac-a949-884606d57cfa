import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import * as _ from 'lodash';
import {PaginationButton} from 'rf-components/pagination/PaginationButton';
import {host} from 'rf-data/siteConfig.json';
import {screenSizes} from 'rf-styles';
const styles = css`
  .pagination-buttons-large {
    display: none;
  }
  
  .pagination-buttons-small {
    display: flex;
    justify-content: center;
  }
  
  @media (min-width: ${screenSizes.s}px) {
    .pagination-buttons-large {
      display: flex;
    }

    .pagination-buttons-small {
      display: none;
    }
  }
`;
export function ButtonList({pageSlug, buttons, size, formatter}) {
    return (
        <div className={`pagination-buttons-${size}`}>
            {_.map(buttons, (page, index) => (
                <PaginationButton url={`${host.url}/${page.pageSlug}`}
                    text={formatter(page)}
                    selected={pageSlug === page.pageSlug}
                    key={index}/>
            ))}
            <style jsx>{styles}</style>
        </div>
    );
}

ButtonList.propTypes = {
    pageSlug:  PropTypes.string,
    buttons:   PropTypes.array,
    size:      PropTypes.string,
    formatter: PropTypes.func
};

import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {FleetsterIcon} from 'rf-icon';
import {colors, screenSizes} from 'rf-styles';
import {removeEndSlash} from 'rf-utils';

const styles = css`
  .pagination-arrow {
    margin-bottom: 0;
  }

  .pagination-arrow:hover {
    cursor: pointer;
  }

  .large-arrows {
    display: none;
  }

  .small-arrows {
    display: flex;
  }

  @media (min-width: ${screenSizes.s}px) {
    .large-arrows {
      display: flex;
    }

    .small-arrows {
      display: none;
    }
  }
`;
export function ArrowButton({showArrow, pageRef, arrowType, arrowSize}) {
    pageRef = removeEndSlash(pageRef);

    // Generate accessible label based on arrow direction
    const ariaLabel = arrowType === 'arrowLeft' ? 'Go to previous page' : 'Go to next page';

    return (
        <div className={`${arrowSize}-arrows`}>
            {showArrow && <div className={'center-content'}>
                <a className={'pagination-arrow'}
                    href={pageRef}
                    aria-label={ariaLabel}
                    title={ariaLabel}>
                    <FleetsterIcon icon={arrowType} iconSize={17} customStyles={{color: colors.blue}}/>
                </a></div>}
            <style jsx>{styles}</style>
        </div>
    );
}

ArrowButton.propTypes = {
    showArrow: PropTypes.bool,
    pageRef:   PropTypes.string,
    arrowType: PropTypes.string,
    arrowSize: PropTypes.string
};

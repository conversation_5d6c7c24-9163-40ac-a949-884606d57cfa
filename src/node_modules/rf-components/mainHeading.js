import React from 'react';
import PropTypes from 'prop-types';

export function MainHeading({index, heading, classNames, style}) {
    if (index === 0) {
        return <h1 className={classNames || 'h1'} style={style}>{heading}</h1>;
    } else {
        return <h2 className={classNames || 'h1'} style={style}>{heading}</h2>;
    }
}

MainHeading.propTypes = {
    heading:    PropTypes.string,
    index:      PropTypes.number,
    classNames: PropTypes.string,
    style:      PropTypes.object
};

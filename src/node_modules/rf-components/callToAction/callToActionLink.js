import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {screenSizes, colors, vSpacing, hSpacing} from 'rf-styles';
import {shouldOpenInNewTab} from 'rf-utils';

const styles = css`
  a {
    width: 100%;
    height: 100%;
    min-height: inherit;
    color: inherit;
    padding: ${hSpacing.xs}px ${vSpacing.s}px;
    text-decoration: none;
    outline: none;
    text-align: center;
    margin-bottom: 0;
    font-size: 12px;
    line-height: 16px;
    box-sizing: border-box;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    a {
      font-size: 14px;
      line-height: 18px;
    }
  }

  @media (min-width: ${screenSizes.xs}px) {
    a {
      font-size: 14px;
      line-height: 18px;
      padding: 0 ${vSpacing.s}px;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    a {
      font-size: 16px;
      line-height: 20px;
    }
  }

  a.light-theme {
    color: ${colors.blue};
  }

  a.hover-enabled:hover {
    color: ${colors.white};
  }
`;

export function CallToActionLink({title, route, link, lightTheme, enableHover = true}) {
    const className = `center-content ${lightTheme ? 'light-theme' : ''} ${enableHover ? 'hover-enabled' : ''}`;

    const href = route?.href;
    if (href) {
        return (
            <>
                <a href={href} className={className}>{title}</a>
                <style jsx>{styles}</style>
            </>
        );
    }

    let openInNewTabProps = {};

    if (shouldOpenInNewTab(link)) {
        openInNewTabProps = {target: '_blank', rel: 'noreferrer'};
    }

    return (
        <a href={link} {...openInNewTabProps} className={className}>
            {title}
            <style jsx>{styles}</style>
        </a>
    );
}

CallToActionLink.propTypes = {
    link:        PropTypes.string,
    lightTheme:  PropTypes.bool,
    className:   PropTypes.string,
    _key:        PropTypes.string,
    title:       PropTypes.string,
    enableHover: PropTypes.bool,
    route:       PropTypes.shape({
        _ref: PropTypes.string
    })
};

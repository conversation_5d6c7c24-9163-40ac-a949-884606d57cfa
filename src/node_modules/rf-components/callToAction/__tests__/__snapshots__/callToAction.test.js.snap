// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CallToAction renders a link that opens in new tab if link is a file 1`] = `
<div
  className="call-to-action"
>
  <a
    className="center-content  hover-enabled"
    href="http://localhost:3000/file.pdf"
    rel="noreferrer"
    target="_blank"
  >
    Link's awakening
    <style />
  </a>
  <style />
</div>
`;

exports[`CallToAction renders a link that opens in the current tab 1`] = `
<div
  className="call-to-action"
>
  <a
    className="center-content  hover-enabled"
    href="http://localhost:3000/somewhere"
  >
    Link's awakening
    <style />
  </a>
  <style />
</div>
`;

exports[`CallToAction renders an internal link if a route is passed in the params 1`] = `
<div
  className="call-to-action"
>
  <a
    className="center-content  hover-enabled"
    href="http://localhost:3000/v4/corsoroute"
  >
    Route's awakening
  </a>
  <style />
  <style />
</div>
`;

exports[`CallToAction renders with light theme 1`] = `
<div
  className="call-to-action light-theme"
>
  <a
    className="center-content light-theme hover-enabled"
    href="http://localhost:3000/somewhere"
  >
    Link's awakening
    <style />
  </a>
  <style />
</div>
`;

exports[`CallToAction renders with light theme and hover disabled 1`] = `
<div
  className="call-to-action light-theme"
>
  <a
    className="center-content light-theme "
    href="http://localhost:3000"
  >
    Link's awakening
    <style />
  </a>
  <style />
</div>
`;

exports[`CallToAction renders without errors 1`] = `
<div
  className="call-to-action"
>
  <a
    className="center-content  hover-enabled"
    rel="noreferrer"
    target="_blank"
  >
    title
    <style />
  </a>
  <style />
</div>
`;

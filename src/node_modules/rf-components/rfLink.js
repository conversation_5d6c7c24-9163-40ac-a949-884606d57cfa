import React from 'react';
import PropTypes from 'prop-types';

import {removeEndSlash} from 'rf-utils';

export function RFLink({route, url, className, styles, children}) {
    if (!route && !url) { return null; }

    const href = route?.href;

    return (
        <>
            {href
                ? <a href={href} className={className}>{children}</a>
                : <a className={className} href={removeEndSlash(url)} target={'_blank'} rel={'noreferrer noopener'}>{children}</a>}
            <style jsx>{styles}</style>
        </>
    );
}

RFLink.propTypes = {
    children:  PropTypes.array,
    className: PropTypes.string,
    route:     PropTypes.object,
    styles:    PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.string
    ]),
    url: PropTypes.string
};


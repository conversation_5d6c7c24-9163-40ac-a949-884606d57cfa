import React from 'react';
import PropTypes from 'prop-types';

import {removeEndSlash, shouldOpenInNewTab} from 'rf-utils';

export function Link({mark, children}) {
    if (mark._type === 'internalLink') {
        const href = mark.href;

        return <a href={href} className={'link'}>{children}</a>;
    }

    let {openInNewTab, href} = mark;
    href = removeEndSlash(href);

    if (!shouldOpenInNewTab(href)) {
        openInNewTab = false;
    }

    return openInNewTab
        ? <a href={href} className={'link'} target={'_blank'} rel={'noopener noreferrer'}>{children}</a>
        : <a href={href} className={'link'}>{children}</a>;
}

Link.propTypes = {
    mark:     PropTypes.object,
    children: PropTypes.node
};

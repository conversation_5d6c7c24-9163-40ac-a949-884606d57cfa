// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SimpleBlockContent - Link Should make external link open in current tab if it is same host even if openInNewTab is true 1`] = `
<a
  className="link"
  href="http://localhost:3000"
>
  huehuebr
</a>
`;

exports[`SimpleBlockContent - Link Should render internal link pointing to correct route 1`] = `
<a
  className="link"
  href="http://localhost:3000/v4/corsoroute"
>
  huehuebr
</a>
`;

exports[`SimpleBlockContent - Link Should render link pointing to new tab 1`] = `
<a
  className="link"
  href="http://www.google.com"
  rel="noopener noreferrer"
  target="_blank"
>
  huehuebr
</a>
`;

exports[`SimpleBlockContent - Link Should render link without pointing to new tab 1`] = `
<a
  className="link"
  href="http://www.google.com"
>
  huehuebr
</a>
`;

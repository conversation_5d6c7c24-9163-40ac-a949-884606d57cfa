import React from 'react';

import {EmbedHTML} from '../embedHTML';

describe('EmbedHTML', () => {
    test('Should return null if node is undefined or if html is nonexistent', () => {
        const htmlWithUndefinedNode = EmbedHTML({});
        const htmlWithUndefinedHTML = EmbedHTML({node: {}});
        const htmlWithNullHTML = EmbedHTML({node: {html: null}});
        const htmlWithEmptyStringHTML = EmbedHTML({node: {html: ''}});

        expect(htmlWithUndefinedNode).toBe(null);
        expect(htmlWithUndefinedHTML).toBe(null);
        expect(htmlWithNullHTML).toBe(null);
        expect(htmlWithEmptyStringHTML).toBe(null);
    });

    test('Should correctly embed HTML', () => {
        const HTML = '<div>huehuebr</div>';
        const component = render(<EmbedHTML node={{html: HTML}}/>);

        expect(component.root.findByType('div').props.dangerouslySetInnerHTML).toEqual({__html: HTML});
    });
});

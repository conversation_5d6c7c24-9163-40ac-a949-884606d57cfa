import {getImageSrcSet, getSizeRules} from '../helpers';

describe('Image component - helpers', () => {
    describe('getSizeRules', () => {
        test('Outputs correct size rules', () => {
            const sizes = [340, 600, 800];
            const rules = getSizeRules(sizes);

            expect(rules[0]).toEqual('(max-width: 341px) 340px');
            expect(rules[1]).toEqual('(max-width: 601px) 600px');
            expect(rules[2]).toEqual('800px');
        });

        test('Last rule remains condition-free even if only 1 element in array', () => {
            const sizes = [800];
            const rules = getSizeRules(sizes);

            expect(rules[0]).toEqual('800px');
        });
    });

    describe('getImageSrcSet', () => {
        const image = {
            _type: 'image',
            asset: {
                _ref:  'image-b56718596084267a4f79129c51096493d1bdce72-225x225-png',
                _type: 'reference'
            }
        };

        test('srcSet is correctly calculated', () => {
            const [width, height] = [10000, 10000];
            let {srcSet} = getImageSrcSet({image, width, height});
            srcSet = srcSet.split(', ');

            expect(srcSet).toMatchSnapshot();
        });

        test('srcSet is only calculated for screenSizes lower than image width', () => {
            const [width, height] = [1026, 10000];
            let {srcSet} = getImageSrcSet({image, width, height});
            srcSet = srcSet.split(', ');

            expect(srcSet).toHaveLength(4);
        });
    });
});

import React from 'react';
import {Image} from '../index';
import {getImageSource, getImageSrcSet} from '../helpers';
jest.mock('../helpers', () => ({
    getImageSource: jest.fn(),
    getImageSrcSet: jest.fn()
}));

jest.mock('next/head', () => {
    return {
        __esModule: true,
        default:    ({children}) => children
    };
});

describe('Image', () => {
    beforeEach(() => {
        getImageSource.mockReturnValue('http://example.com/image-source.jpg');
        getImageSrcSet.mockReturnValue({srcSet: 'http://example.com/image-srcSet.jpg 100w', sizes: '100px'});
    });

    it('should render Image with intrinsic layout correctly', () => {
        const component = render(
            <Image
                image={{}}
                width={500}
                height={500}
            />
        );

        const jsonComponent = component.toJSON();
        expect(jsonComponent).toMatchSnapshot();
    });

    it('should render Image with fill layout correctly', () => {
        const component = render(
            <Image
                image={{}}
                width={500}
                height={500}
                layout={'fill'}
            />
        );

        const jsonComponent = component.toJSON();
        expect(jsonComponent).toMatchSnapshot();
    });

    it('should include preload link in head when priority is true', () => {
        const component = render(
            <Image
                image={{}}
                width={500}
                height={500}
                priority={true}
            />
        );

        const jsonComponent = component.toJSON();
        expect(jsonComponent).toMatchSnapshot();
    });

    it('should not include preload link in head when priority is false', () => {
        const component = render(
            <Image
                image={{}}
                width={500}
                height={500}
                priority={false}
            />
        );

        const jsonComponent = component.toJSON();
        expect(jsonComponent).toMatchSnapshot();
    });
});

import _ from 'lodash';

import {screenSizes} from 'rf-styles';
import {imageBuilder} from 'rf-data';

export function getImageSource({image, width, height}) {
    let imageSource = imageBuilder.image(image).auto('format');

    if (width) { imageSource = imageSource.width(width); }
    if (height) { imageSource = imageSource.height(height); }

    return imageSource.url();
}

export function getSizeRules(sizes) {
    /* eslint-disable-next-line no-extra-parens */
    return _.map(sizes, (size, index) => (index < sizes.length - 1 ? `(max-width: ${size + 1}px) ${size}px` : `${size}px`));
}

export function getImageSrcSet({image, width, height, minWidth = 0}) {
    if (!width || !height) { return {}; }

    const sizes = [..._.filter(screenSizes, s => s < width && s >= minWidth), width];

    let srcs = [];
    for (const size of sizes) {
        const scaledHeight = _.round(height * (size / width));
        srcs.push(`${getImageSource({image, width: size, height: scaledHeight})} ${size}w`);
    }

    return {srcSet: srcs.join(', '), sizes: getSizeRules(sizes).join(', ')};
}

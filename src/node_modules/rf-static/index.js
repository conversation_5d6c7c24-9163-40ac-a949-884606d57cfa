/*
 * IMPORTANT: functions in this file should only be imported
 * to be used in getStaticProps or getStaticPaths.
 */

import _ from 'lodash';
import {host, language, logo} from 'rf-data/siteConfig.json';
import {imageBuilder} from 'rf-data';
import {getAllLanguageSlugValues, removeEndSlash} from 'rf-utils';

import news from '../../../sanityStudioData/news.json';
import changelog from '../../../sanityStudioData/changelog.json';
import routes from '../../../sanityStudioData/routes.json';

const newsUrls = {en: 'news', de: 'neuigkeiten', nl: 'nieuws', es: 'noticias'};
const changelogUrls = {en: 'product-updates', de: 'produkt-updates', es: 'actualizaciones', nl: 'product-updates'};
export const newsIndexName = newsUrls[language];
export const changelogIndexName = changelogUrls[language];

const newsRoutes = news.i18n[language].routes;
const changelogRoutes = changelog.i18n[language].routes;

// ToDo change news/changelog datastructure to match routes.json -> closes https://gitlab.fleetster.de/fleetster/fleetster-website-frontend/-/issues/635
// ToDo handle news and changelog separately -> closes https://gitlab.fleetster.de/fleetster/fleetster-website-frontend/-/issues/636

export function buildOpenGraphImage({title, width, height, openGraphImage}) {
    return {
        url: imageBuilder.image(openGraphImage).width(width).height(height).url(),
        alt: title,
        width,
        height
    };
}

export function getOpenGraphImages(title, openGraphImage) {
    if (! openGraphImage) {
        return [];
    }

    return [
        buildOpenGraphImage({title, width: 1200, height: 630, openGraphImage}),
        buildOpenGraphImage({title, width: 800, height: 600, openGraphImage}),
        buildOpenGraphImage({title, width: 600, height: 600, openGraphImage})
    ];
}

function getPageData(slug) {
    let route;
    let page;

    route = routes[slug];
    route = route || routes[`${slug}/`];

    page = _.get(route, `i18n.${language}.page.i18n.${language}`);

    return {route, page};
}

export function getRegularPageStaticProps(slug) {
    const {route, page} = getPageData(slug);

    if (!page) { return {}; }

    const canonical = removeEndSlash(`${host.url}/${slug}`);

    const disallowRobots = !!route.i18n[language].disallowRobots;
    const openGraph = {images: getOpenGraphImages(page?.title || 'Missing title', page?.openGraphImage)};

    _.assign(route, {slug, host, logo, canonical, openGraph, disallowRobots});

    return {props: {route}};
}

export function getRegularPageStaticPaths() {
    const paths = _.transform(routes, (paths, route, pageSlug) => {
        const pageContent = _.get(route, `i18n.${language}.page.i18n.${language}.content`);

        if (_.isEmpty(pageContent)) {
            return;
        }

        const slug = pageSlug === '' ? null : pageSlug.split('/');

        paths.push({params: {slug}});
    }, []);

    return paths;
}

export function getNewsRoute(slug) {
    const page = newsRoutes[slug];
    page.slug = slug;
    page.id = slug;
    page.host = host;
    page.logo = logo;
    page.i18n = getAllLanguageSlugValues(newsUrls);

    return page;
}

export function getNewsPaths() {
    return _.transform(newsRoutes, (paths, route, pageSlug) => {
        if (!_.get(route, 'content')) {
            return;
        }

        const slug = pageSlug === ''
            ? null
            : pageSlug.split('/');

        paths.push({params: {slug}});
    }, []);
}

export function getChangelogRoute(pageSlug) {
    const page = changelogRoutes[pageSlug];
    page.slug = pageSlug;
    page.id = pageSlug;
    page.host = host;
    page.logo = logo;
    page.i18n = getAllLanguageSlugValues(changelogUrls);

    return page;
}

export function getChangelogPaths() {
    return _.transform(changelogRoutes, (paths, route, pageSlug) => {
        if (!_.get(route, 'content')) {
            return;
        }

        const slug = pageSlug === ''
            ? null
            : pageSlug.split('/');

        paths.push({params: {slug}});
    }, []);
}

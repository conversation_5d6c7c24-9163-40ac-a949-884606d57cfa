import {fetchDocuments, getDocument} from '../index';
import {createClient} from '@sanity/client';

describe('Sanity Client Adapter', () => {
    let mockFetch;
    let mockGetDocument;

    beforeAll(() => {
        const sanityClient = createClient();
        mockFetch = sanityClient.fetch;
        mockGetDocument = sanityClient.getDocument;
    });

    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('fetchDocuments', () => {
        test('should fetch and return documents with translations', async() => {
            const query = '*[_type == "page"]';
            const params = {};
            const documents = [{_id: 'doc1', label: 'something', language: 'en'}];
            const translations = {
                i18n: [
                    {value: {_id: 'doc1', label: 'something', language: 'en'}},
                    {value: {_id: 'doc2', label: 'otra cosita', language: 'es'}}
                ]
            };

            mockFetch.mockResolvedValueOnce(documents);
            mockFetch.mockResolvedValueOnce(translations);

            const result = await fetchDocuments(query, params);

            expect(mockFetch).toHaveBeenCalledTimes(2);
            expect(mockFetch).toHaveBeenCalledWith(expect.stringMatching(/value->/), {docId: 'doc1', language: ''});
            expect(result[0].i18n).toEqual({
                en: {
                    _id:      'doc1',
                    label:    'something',
                    language: 'en'
                },
                es: {
                    _id:      'doc2',
                    label:    'otra cosita',
                    language: 'es'
                }
            });
        });

        test('should switch projection to filter out other languages from result if language param is present', async() => {
            const query = '*[_type == "page"]';
            const params = {};
            const documents = [{_id: 'doc1', label: 'something', language: 'en'}];
            const translations = {
                i18n: [
                    {value: {_id: 'doc1', label: 'something', language: 'en'}}
                ]
            };

            mockFetch.mockResolvedValueOnce(documents);
            mockFetch.mockResolvedValueOnce(translations);

            const result = await fetchDocuments(query, params, 'en');

            expect(mockFetch).toHaveBeenCalledWith(expect.stringMatching(/_key == \$language/), {docId: 'doc1', language: 'en'});
            expect(result[0].i18n).toEqual({
                en: {
                    _id:      'doc1',
                    label:    'something',
                    language: 'en'
                }
            });
        });

        test('should fetch and return a single document with translations', async() => {
            const query = '*[_id == $id][0]';
            const params = {id: 'doc1'};
            const documents = {_id: 'doc1', label: 'something', language: 'en'};
            const translations = {
                i18n: [
                    {value: {_id: 'doc1', label: 'something', language: 'en'}}
                ]
            };

            mockFetch.mockResolvedValueOnce(documents);
            mockFetch.mockResolvedValueOnce(translations);

            const result = await fetchDocuments(query, params);

            expect(mockFetch).toHaveBeenCalledWith(query, params);
            expect(result.i18n).toEqual({
                en: {
                    _id:      'doc1',
                    label:    'something',
                    language: 'en'
                }
            });
        });
    });

    describe('getDocument', () => {
        test('should get a document and set translations', async() => {
            const id = 'doc1';
            const document = {_id: 'doc1', language: 'en'};
            const translations = {
                i18n: [
                    {value: {_id: 'doc1', label: 'something', language: 'en'}}
                ]
            };

            mockGetDocument.mockResolvedValueOnce(document);
            mockFetch.mockResolvedValueOnce(translations);

            const result = await getDocument(id);

            expect(mockGetDocument).toHaveBeenCalledWith(id);
            expect(mockFetch).toHaveBeenCalledTimes(1);
            expect(result.i18n).toEqual({
                en: {
                    _id:      'doc1',
                    label:    'something',
                    language: 'en'
                }
            });
        });

        test('should set itself as its own translation if translation metadata is not found', async() => {
            const id = 'doc1';
            const document = {_id: 'doc1', label: 'watehver', language: 'en'};
            const translations = {};

            mockGetDocument.mockResolvedValueOnce(document);
            mockFetch.mockResolvedValueOnce(translations);
            mockFetch.mockResolvedValueOnce({...document});

            const result = await getDocument(id);

            expect(mockGetDocument).toHaveBeenCalledWith(id);
            expect(mockFetch).toHaveBeenCalledTimes(2);
            expect(result.i18n).toEqual({
                en: {
                    _id:      'doc1',
                    label:    'watehver',
                    language: 'en'
                }
            });
        });
    });
});

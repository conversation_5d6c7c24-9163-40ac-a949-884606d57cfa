import PropTypes from 'prop-types';
import React from 'react';
import {
    changelogIndexName,
    getRegularPageStaticPaths,
    getRegularPageStaticProps,
    getChangelogPaths,
    getChangelogRoute,
    getNewsPaths,
    getNewsRoute,
    newsIndexName
} from 'rf-static';
import {RegularPage, NewsPage, ChangelogPage} from '../pageComponents';

// ToDo remove lodash from src -> closes https://gitlab.fleetster.de/fleetster/fleetster-website-frontend/-/issues/631
// Todo verify we actually need normalize css -> closes https://gitlab.fleetster.de/fleetster/fleetster-website-frontend/-/issues/627
// Todo remove sanity libraries from src -> closes https://gitlab.fleetster.de/fleetster/fleetster-website-frontend/-/issues/629
// Todo switch to context instead of node_modules for global data access -> closes https://gitlab.fleetster.de/fleetster/fleetster-website-frontend/-/issues/630

export const getStaticPaths = () => {
    const regularPagePaths = getRegularPageStaticPaths();
    const newsPagePaths = getNewsPaths();
    const changelogsPagePaths = getChangelogPaths();

    return {paths: [...regularPagePaths, ...newsPagePaths, ...changelogsPagePaths], fallback: false};
};

function isNewsSlug(slug) {
    return slug?.[0] === newsIndexName && (!slug?.[1] || slug?.[1].match(/^\d{4}$/));
}

function isChangelogSlug(slug) {
    return slug?.[0] === changelogIndexName && (!slug?.[1] || slug?.[1].match(/^q[1-4]-\d{4}$/));
}

export const getStaticProps = ({params: {slug}}) => {
    let slugString = slug?.join('/') || '';

    if (isNewsSlug(slug)) {
        return {props: {slug: slugString, isNewsPage: true, route: getNewsRoute(slugString)}};
    }

    if (isChangelogSlug(slug)) {
        return {props: {slug: slugString, isChangelogPage: true, route: getChangelogRoute(slugString)}};
    }

    return getRegularPageStaticProps(slugString);
};

function Page({route, slug, isChangelogPage, isNewsPage}) {
    if (isNewsPage) {
        return <NewsPage route={route} slug={slug}/>;
    }

    if (isChangelogPage) {
        return <ChangelogPage route={route} slug={slug}/>;
    }

    return <RegularPage route={route}/>;
}

Page.propTypes = {
    slug:            PropTypes.string,
    route:           PropTypes.object,
    isNewsPage:      PropTypes.bool,
    isChangelogPage: PropTypes.bool
};

export default Page;

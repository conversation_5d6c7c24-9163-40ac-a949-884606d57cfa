import {NextSeo} from 'next-seo';
import React from 'react';

import {T, t} from 'rf-i18n';

import {Layout} from '../layout';

export default function Custom404Page() {
    return (
        <Layout route={{slug: '404', host: null, image: null, breadcrumbsList: null}}>
            <NextSeo title={t('seo.title.notFound')} noindex={'true'} />
            <div className={'rf-flex-container'} style={{height: '400px'}}>
                <div style={{flex: 1}}/>
                <div className={'rf-cell center-content'}>
                    <h1><T>seo.title.notFound</T></h1>
                </div>
                <div style={{flex: 1}}/>
            </div>
        </Layout>
    );
}


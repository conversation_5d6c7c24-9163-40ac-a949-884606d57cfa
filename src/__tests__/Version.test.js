import React from 'react';

describe('VersionPage', () => {
    test('returns version in props', () => {
        const {getStaticProps} = require('../pages/version');
        const props = getStaticProps();

        expect(props).toHaveProperty('props.versionString');
    });

    test('renders correctly', () => {
        const {default: Version} = require('../pages/version');

        const component = render(<Version versionString={'v0.0'}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders correctly when version not passed in', () => {
        const {default: Version} = require('../pages/version');

        const component = render(<Version/>);

        expect(component).toMatchSnapshot();
    });
});

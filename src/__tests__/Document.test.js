import React from 'react';

describe('Document', () => {
    const MockHtml = ({lang, children}) => <html lang={lang}>{children}</html>;
    const MockMain = ({children}) => <main>{children}</main>;
    const MockHead = ({title, children}) => <head title={title}>{children}</head>;
    const MockScript = ({children}) => <script>{children}</script>;

    jest.mock('next/document', () => ({
        Head:       MockHead,
        Html:       MockHtml,
        Main:       MockMain,
        NextScript: MockScript
    }));

    test('renders correctly', () => {
        const {default:Document} = require('../pages/_document');
        const component = render(<Document/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});

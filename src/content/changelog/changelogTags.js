import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import * as _ from 'lodash';

import {colors, fontWeights, hSpacing} from 'rf-styles';
import {t} from 'rf-i18n';

import {tagColorMapping} from './tagColorMapping';

const styles = css`
  .tags-section {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .tag {
    margin: 0 5px ${hSpacing.xxs}px 0;
    padding: 2px 10px;
    border-radius: 20px;
    color: ${colors.white};
    font-weight: ${fontWeights.semiBold};
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

`;

export function ChangelogTags({tags}) {
    return (
        <div className={'tags-section'}>
            {_.map(tags, (tag, index) => {
                return (<div className={'tag'}
                    key={index}
                    aria-label={`Tag: ${t(`tags.${tag.value}`)}`}
                    style={{background: tagColorMapping[tag.value] || colors.gray}}>
                    {t(`tags.${tag.value}`)}
                </div>);
            })}
            <style jsx>{styles}</style>
        </div>
    );
}

ChangelogTags.propTypes = {
    tags:               PropTypes.array,
    defaultColoredTags: PropTypes.array
};

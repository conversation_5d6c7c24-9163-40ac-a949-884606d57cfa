import * as _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';

import css from 'styled-jsx/css';

import {T} from 'rf-i18n';
import {screenSizes, vSpacing} from 'rf-styles';

import {ChangelogEntry} from './changelogEntry';

const styles = css`
  .changelog-list {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    width: 100%;
    min-width: 320px;
    margin-bottom: ${vSpacing.s}px;
  }
  
  .section-title {
    margin-bottom: ${vSpacing.m}px;
  }
  
  .changelog-list-content {
    width: 85%;
    margin-top: ${vSpacing.m}px;
  }

  @media (min-width: ${screenSizes.m}px) { .changelog-list {
    margin-bottom: ${vSpacing.m}px;  
  }}
  
  @media (min-width: ${screenSizes.l}px) { .changelog-list-content {
    width: 70%;
  }}
  @media (min-width: ${screenSizes.xl}px) { .changelog-list-content {
    width: 60%;
  }}`;

export function ChangelogList({changelogs = []}) {
    return (
        <div className={'changelog-list'}>
            <div className={'changelog-list-content'}>
                <h1 className={'h2 center-content section-title'}><T>sectionTitle.productUpdates</T></h1>
                {_.map(changelogs, (changelog, index) => (<ChangelogEntry
                    key={index}
                    changelog={changelog}
                    index={index}/>))
                }
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

ChangelogList.propTypes = {
    changelogs: PropTypes.array
};

import React from 'react';
import PropTypes from 'prop-types';
//TODO don't import this way, local import or move video to node module
import {Video} from '../sections/videoSection/video';
import {ChangelogImage} from './changelogImage';

export function ChangelogMedia({image, imageAlt, videoUrl}) {
    if (videoUrl) {
        return <Video url={videoUrl}/>;
    }

    if (image) {
        return <ChangelogImage image={image} imageAlt={imageAlt}/>;
    }

    return null;
}

ChangelogMedia.propTypes = {
    image:    PropTypes.object,
    imageAlt: PropTypes.string,
    videoUrl: PropTypes.string
};

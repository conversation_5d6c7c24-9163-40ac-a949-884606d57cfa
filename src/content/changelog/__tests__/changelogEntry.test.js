import React from 'react';
import _ from 'lodash';

describe('changelog', () => {
    const changelog = {
        callToAction: {
            _type: 'callToAction',
            link:  '/v4/request/price-request',
            title: 'Read more'
        },
        content: [
            {
                _type:    'embedHTML',
                style:    'normal',
                children: [
                    {text: 'lorem ipsum 1'}
                ]
            }
        ],
        releaseDate:    '2020-12-24',
        releaseVersion: '3.90',
        title:          'Testing Changelog Q 4',
        quarter:        4,
        year:           2020,
        pageSlug:       'product-updates/q4-2020',
        image:          {
            asset:  {_ref: 'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg'},
            height: 3000,
            width:  2000
        },
        tags: [{
            value: 'fleetManagement',
            label: 'Fleet Management'
        }],
        language: 'de'
    };

    beforeAll(() => {
        jest.restoreAllMocks();
    });

    beforeEach(() => {
        jest.doMock('rf-components/simpleBlockContent', () => ({
            __esModule: true,
            default:    ({blocks, ...rest}) => <div {...rest}>{blocks}</div> // eslint-disable-line react/display-name, react/prop-types
        }));
    });

    test('renders correctly', () => {
        const {ChangelogEntry} = require('../changelogEntry');

        const component = render(<ChangelogEntry changelog={changelog}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders dash on releaseVersion if it is not present', () => {
        const {ChangelogEntry} = require('../changelogEntry');
        let changelogCopy = _.cloneDeep(changelog);
        delete changelogCopy.releaseVersion;

        const component = render(<ChangelogEntry changelog={changelogCopy}/>);

        expect(component.root.findByProps({className: 'changelog-release link'}).children[0]).toBe('24.12.2020, -');
    });

    test('renders no CTA if title is not present', () => {
        const {ChangelogEntry} = require('../changelogEntry');
        let changelogCopy = _.cloneDeep(changelog);
        changelogCopy.callToAction = {};

        const component = render(<ChangelogEntry changelog={changelogCopy}/>);

        expect(() => component.root.findByType('a')).toThrow();
    });

    test('renders video if image is not present and videoUrl is', () => {
        const {ChangelogEntry} = require('../changelogEntry');
        let changelogCopy = _.cloneDeep(changelog);
        delete changelogCopy.image;

        changelogCopy.videoUrl = 'https://www.youtube.com/watch?v=huehuebr';

        const component = render(<ChangelogEntry changelog={changelogCopy}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('video does not show if link is wrong', () => {
        const {ChangelogEntry} = require('../changelogEntry');
        let changelogCopy = _.cloneDeep(changelog);
        delete changelogCopy.image;

        changelogCopy.videoUrl = 'https://youtu.be/watch?v=huehuebr';

        const component = render(<ChangelogEntry changelog={changelogCopy}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`changelog renders correctly 1`] = `
<section
  className="changelog-card"
  id="testing-changelog-q-4"
>
  <div
    className="rf-cell"
  >
    <h2
      className="changelog-card-title h4"
    >
      Testing Changelog Q 4
    </h2>
    <h3
      className="changelog-release"
    >
      24.12.2020, 3.90
    </h3>
    <div
      className="tags-section"
    >
      <div
        aria-label="Tag: tags.fleetManagement"
        className="tag"
        style={
          {
            "background": "#B32D9A",
          }
        }
      >
        tags.fleetManagement
      </div>
      <style />
    </div>
    <div
      className="changelog-card-image"
    >
      <div>
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg"
            decoding="async"
            height={3000}
            loading="lazy"
            sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, (max-width: 1922px) 1921px, 2000px"
            src="https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=2000&h=3000&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=1,0,1998,3000&w=361&h=542&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=1,0,1999,3000&w=641&h=962&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=0,1,2000,2999&w=1025&h=1537&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=0,1,2000,2999&w=1281&h=1921&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=1681&h=2522&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=1921&h=2882&auto=format 1921w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=2000&h=3000&auto=format 2000w"
            style={
              {
                "height": "auto",
                "maxHeight": 3000,
                "maxWidth": 2000,
                "width": "100%",
              }
            }
            width={2000}
          />
        </div>
      </div>
      <style />
    </div>
    <div
      className="changelog-description"
    />
    <div
      className="call-to-action-wrapper"
    >
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="/v4/request/price-request"
          rel="noreferrer"
          target="_blank"
        >
          Read more
          <style />
        </a>
        <style />
      </div>
    </div>
  </div>
  <style />
</section>
`;

exports[`changelog renders video if image is not present and videoUrl is 1`] = `
<section
  className="changelog-card"
  id="testing-changelog-q-4"
>
  <div
    className="rf-cell"
  >
    <h2
      className="changelog-card-title h4"
    >
      Testing Changelog Q 4
    </h2>
    <h3
      className="changelog-release"
    >
      24.12.2020, 3.90
    </h3>
    <div
      className="tags-section"
    >
      <div
        aria-label="Tag: tags.fleetManagement"
        className="tag"
        style={
          {
            "background": "#B32D9A",
          }
        }
      >
        tags.fleetManagement
      </div>
      <style />
    </div>
    <div
      className="changelog-description"
    />
    <div
      className="call-to-action-wrapper"
    >
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="/v4/request/price-request"
          rel="noreferrer"
          target="_blank"
        >
          Read more
          <style />
        </a>
        <style />
      </div>
    </div>
  </div>
  <style />
</section>
`;

exports[`changelog video does not show if link is wrong 1`] = `
<section
  className="changelog-card"
  id="testing-changelog-q-4"
>
  <div
    className="rf-cell"
  >
    <h2
      className="changelog-card-title h4"
    >
      Testing Changelog Q 4
    </h2>
    <h3
      className="changelog-release"
    >
      24.12.2020, 3.90
    </h3>
    <div
      className="tags-section"
    >
      <div
        aria-label="Tag: tags.fleetManagement"
        className="tag"
        style={
          {
            "background": "#B32D9A",
          }
        }
      >
        tags.fleetManagement
      </div>
      <style />
    </div>
    <div
      className="changelog-description"
    />
    <div
      className="call-to-action-wrapper"
    >
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="/v4/request/price-request"
          rel="noreferrer"
          target="_blank"
        >
          Read more
          <style />
        </a>
        <style />
      </div>
    </div>
  </div>
  <style />
</section>
`;

import _ from 'lodash';

function parseYoutubeUrl(url, autoplay) {
    const videoIdRegex = /watch\?v=(.+?)(&|$)/;
    const videoId = url.match(videoIdRegex)[1];

    let embedUrl = `https://www.youtube.com/embed/${videoId}`;

    if (autoplay) {
        embedUrl = `${embedUrl}?playlist=${videoId}&autoplay=1&mute=1&loop=1`;
    }

    return {videoId, url: embedUrl, type: 'youtube'};
}

function parseVimeoUrl(url, autoplay) {
    const videoIdRegex = _.endsWith(url, '/') ? '[^/]+(?=/$)' : '[^/]+$';
    const videoId = url.match(videoIdRegex)[0];

    let embedUrl = `https://player.vimeo.com/video/${videoId}`;

    if (autoplay) {
        embedUrl = `${embedUrl}?autoplay=1&loop=1&autopause=0&muted=1`;
    }

    return {videoId, url: embedUrl, type: 'vimeo'};
}

export function parseVideoUrl(url, autoplay = true) {
    if (_.includes(url, 'youtube')) {
        return parseYoutubeUrl(url, autoplay);
    }

    if (_.includes(url, 'vimeo')) {
        return parseVimeoUrl(url, autoplay);
    }

    return {};
}

export function generateVideoStructuredDataHtml(videoData) {
    if (_.isEmpty(videoData)) {
        return null;
    }

    const structuredData = {
        '@context':           'https://schema.org',
        '@type':              'VideoObject',
        name:                 videoData.title,
        description:          videoData.description,
        uploadDate:           videoData.publishedAt,
        thumbnailUrl:         _.get(videoData, 'thumbnails.standard.url', ''),
        duration:             videoData.duration,
        embedUrl:             `https://www.youtube.com/embed/${videoData.id}`,
        interactionStatistic: {
            '@type':              'InteractionCounter',
            interactionType:      {'@type': 'http://schema.org/WatchAction'},
            userInteractionCount: parseInt(videoData.viewCount)
        }
    };

    return {
        __html: JSON.stringify(structuredData)
    };
}

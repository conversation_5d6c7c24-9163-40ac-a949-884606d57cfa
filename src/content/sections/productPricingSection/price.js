import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {screenSizes, colors, fontWeights, vSpacing} from 'rf-styles';

import {currencies} from './priceHelpers';

const styles = css`
  .product-price {
    font-weight: ${fontWeights.extraBold};
    font-size: 30px;
    line-height: 30px;
    color: ${colors.blue};
    margin-top: ${vSpacing.xs * 2}px;
    margin-bottom: ${vSpacing.xs}px;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .product-price {
      font-size: 34px;
      line-height: 34px;
    }
  }
`;

function Price({price, currency}) {
    return (
        <>
            <div className={'product-price'}>{currencies[currency]?.format(price)}</div>
            <style jsx>{styles}</style>
        </>
    );
}

Price.propTypes = {
    price:    PropTypes.number,
    currency: PropTypes.string
};

export {Price};

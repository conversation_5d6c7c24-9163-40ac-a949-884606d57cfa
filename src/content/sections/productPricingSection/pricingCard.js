import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, hSpacing, fontWeights, screenSizes} from 'rf-styles';
import {T} from 'rf-i18n';

import {PricingCardContent} from './pricingCardContent';

export const mostPopularHeaderHeight = 26;

const styles = css`
  .pricing-card {
    display: flex;
    padding: 0;
    border: 1px solid ${colors.lightGray};
    border-radius: 100%;
    position: relative;
    align-self: stretch;
    flex-basis: 100%;
    min-width: 100%;
  }

  @media (min-width: ${screenSizes.s}px) {
    .pricing-card {
      min-width: unset;
    }
  }

  .most-popular-title {
    position: absolute;
    height: ${mostPopularHeaderHeight}px;
    background-color: ${colors.blue};
    border-radius: 20px;
    padding: 0 ${hSpacing.s}px;
    left: 50%;
    transform: translateX(-50%);
    top: -${mostPopularHeaderHeight / 2}px;
    white-space: nowrap;
  }
  
  .most-popular-title span {
    font-size: 12px;
    line-height: 14px;
    font-weight: ${fontWeights.bold};
    color: white;
    margin: 0;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .most-popular-title span {
      font-size: 14px;
      line-height: 16px;
    }
  }
`;

export function PricingCard(props) {
    return (
        <div className={'rf-cell pricing-card'}>
            {props.isMostPopular &&
                <div className={'center-content most-popular-title'}>
                    <span><T>carsharing.prices.mostPopular</T></span>
                </div>}
            <PricingCardContent {...props}/>
            <style jsx>{styles}</style>
        </div>
    );
}

PricingCard.propTypes = {
    isMostPopular:   PropTypes.bool,
    heading:         PropTypes.string,
    description:     PropTypes.string,
    price:           PropTypes.string,
    condition:       PropTypes.string,
    callToAction:    PropTypes.object,
    plus:            PropTypes.string,
    features:        PropTypes.array,
    pricePercentage: PropTypes.number
};

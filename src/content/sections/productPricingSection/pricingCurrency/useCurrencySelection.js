import {useState, useEffect} from 'react';
import _ from 'lodash';

import {DOLLAR_CURRENCY_KEY, EURO_CURRENCY_KEY} from '../constants';

const currencyMap = {
    'en-US': DOLLAR_CURRENCY_KEY
};

function getPreferredCurrency() {
    const userLang = _.get(navigator, 'languages[0]', navigator.language);

    return currencyMap[userLang] || EURO_CURRENCY_KEY;
}

export function useCurrencySelection() {
    const [selectedCurrency, setCurrency] = useState(EURO_CURRENCY_KEY);

    useEffect(() => {
        setCurrency(getPreferredCurrency());
    }, []);

    return {selectedCurrency, setCurrency};
}

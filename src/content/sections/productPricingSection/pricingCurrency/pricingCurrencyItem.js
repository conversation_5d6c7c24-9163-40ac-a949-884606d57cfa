import React from 'react';
import css from 'styled-jsx/css';
import PropTypes from 'prop-types';

import {FleetsterIcon} from 'rf-icon';
import {colors, fontWeights} from 'rf-styles';

import {CURRENCY_BOX_HEIGHT, PRICING_FILTERS_BORDER_RADIUS} from '../constants';

const styles = css`
  .list-item {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .dropdown-item {
    display: flex;
    justify-content: flex-start;
    background-color: ${colors.white};
    color: ${colors.blue};
    border: 5px solid white;
    border-radius: ${PRICING_FILTERS_BORDER_RADIUS}px;
    cursor: pointer;
    text-align: center;
    list-style: none;
    height: ${CURRENCY_BOX_HEIGHT - 10}px;
    width: calc(100% - 10px);
    max-width: calc(100% - 10px);
  }
  
  .dropdown-item .label-container {
    min-width: 55%;
    text-align: left;
    margin-left: 12px;
  }

  .dropdown-item h6 {
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 14px;
    line-height: 16px;
    font-weight: ${fontWeights.bold};
  }

  .dropdown-item p {
    color: ${colors.blue};
    font-size: 12px;
    line-height: 16px;
    font-weight: ${fontWeights.semiBold};
    margin: 0;
  }

  .active {
    background-color: ${colors.blue};
    color: ${colors.white};
  }

  .active p {
    color: ${colors.white};
  }

  .dropdown-item:hover, .dropdown-item:hover p {
    background-color: ${colors.blue};
    color: ${colors.white};
  }
`;

export function PricingCurrencyItem({onClick, isHeader, currency}) {
    const content = (
        <div
            className={`dropdown-item${isHeader ? ' active' : ''}`}
            onClick={onClick}>
            <div className={'label-container'}>
                <h6>{currency.label}</h6>
                <p>{currency.altLabel}</p>
            </div>
            <FleetsterIcon
                icon={isHeader ? 'caretDown' : ''} iconSize={20}
                customStyles={{color: colors.white, alignSelf: 'center'}}/>
            <style jsx>{styles}</style>
        </div>
    );

    if (isHeader) {
        return content;
    }

    return (
        <li className={'list-item'}>
            {content}
        </li>
    );
}

PricingCurrencyItem.propTypes = {
    onClick:  PropTypes.func,
    isHeader: PropTypes.bool,
    currency: PropTypes.object
};

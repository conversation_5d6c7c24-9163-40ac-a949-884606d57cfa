import React, {useState} from 'react';
import _ from 'lodash';
import css from 'styled-jsx/css';
import PropTypes from 'prop-types';

import {colors} from 'rf-styles';

import {currencies} from '../priceHelpers';
import {CURRENCY_BOX_HEIGHT, CURRENCY_BOX_WIDTH, PRICING_FILTERS_BORDER_RADIUS} from '../constants';
import {PricingCurrencyItem} from './pricingCurrencyItem';

const styles = css`
  .pricing-currencies {
    display: flex;
    border-radius: ${PRICING_FILTERS_BORDER_RADIUS}px;
    background-color: ${colors.white};
    width: ${CURRENCY_BOX_WIDTH}px;
    max-width: ${CURRENCY_BOX_WIDTH}px;
    height: ${CURRENCY_BOX_HEIGHT}px;
    align-items: center;
    user-select: none;
    flex-grow: 1;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
    position: relative;
    transition: ease-in 0.1s;
  }
  
  .open {
    border-radius: ${PRICING_FILTERS_BORDER_RADIUS}px ${PRICING_FILTERS_BORDER_RADIUS}px 0 0;
  }
  
  ul {
    position: absolute;
    width: ${CURRENCY_BOX_WIDTH}px;
    top: ${CURRENCY_BOX_HEIGHT}px;
    padding: 0;
    margin: 0;
    background-color: ${colors.white};
    z-index: -10;
    box-shadow: 0 8px 8px -8px rgba(0, 0, 0, 0.15);
    border-radius: 0 0 ${PRICING_FILTERS_BORDER_RADIUS}px ${PRICING_FILTERS_BORDER_RADIUS}px;
    opacity: 0;
    transition: opacity ease-in 0.1s;
  }
  
  .menu-open {
    opacity: 1;
    z-index: 1;
  }
`;

const getUniqueCurrencies = prices => {
    if (!prices) {
        return [];
    }
    const currencies = prices
        .map(price => price?.map(price => price.currency))
        .reduce((acc, currency) => acc.concat(currency), []);

    return [...new Set(currencies)];
};

export function PricingCurrency({selected, set, prices}) {
    const uniqueCurrencies = getUniqueCurrencies(prices);
    const [isOpen, setIsOpen] = useState(false);
    const toggle = () => setIsOpen(isOpen => !isOpen);
    const onSelect = selected => {
        set(selected);
        setIsOpen(false);
    };

    if (uniqueCurrencies?.length > 1) {
        return (<div className={`pricing-currencies${isOpen ? ' open' : ''}`}>
            <PricingCurrencyItem
                currency={currencies[selected]}
                isHeader
                onClick={toggle}/>
            <ul className={isOpen ? 'menu-open' : ''}>
                {_.keys(currencies)
                    .filter(currency => currency !== selected)
                    .map(currency => (
                        <PricingCurrencyItem
                            key={currency}
                            currency={currencies[currency]}
                            onClick={() => onSelect(currency)}/>
                    ))}
            </ul>
            <style jsx>{styles}</style>
        </div>);
    }

    return null;
}

PricingCurrency.propTypes = {
    selected: PropTypes.string,
    set:      PropTypes.func,
    prices:   PropTypes.array
};

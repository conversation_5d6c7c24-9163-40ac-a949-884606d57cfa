import React, {useState} from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {colors, screenSizes, vSpacing} from 'rf-styles';

import {PricingCard} from './pricingCard';
import {HeroBackgroundImage} from '../hero/heroBackgroundImage';
import {CarouselProvider} from '../hero/carousel';
import {PricingCarousel} from './pricingCarousel';
import {PricingPeriod} from './pricingPeriod';
import {PricingCurrency} from './pricingCurrency';
import {useCurrencySelection} from './pricingCurrency/useCurrencySelection';

const styles = css`
  .product-pricing-section {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    padding-top: ${vSpacing.s}px;
    padding-bottom: ${vSpacing.s}px;
  }

  @media (min-width: ${screenSizes.l}px) { .product-pricing-section {
    padding-top: ${vSpacing.m}px;
    padding-bottom: ${vSpacing.m}px;
  }}
  
  .pricing-tabs {
    display: flex;
    width: 100%;
    justify-content: space-between;
    flex-flow: column;
    row-gap: 10px;
  }

  @media (min-width: ${screenSizes.xs}px) { .pricing-tabs {
    flex-flow: row;
  }}

  .observation-text {
    font-size: 12px;
    color: ${colors.darkGray};
    font-style: italic;
  }
`;

export function ProductPricingSection({pricings = [], periods = [], observationText, backgroundImage, index}) {
    const [selectedPeriod, setPeriod] = useState(_.find(periods, {default: true}));
    const {selectedCurrency, setCurrency} = useCurrencySelection();
    const optimizeForLCP = index < 2;
    const cards = _.take(pricings, 3);
    const prices = _.map(cards, 'prices');

    return (
        <section className={'page-horizontal-padding section-vertical-padding product-pricing-section'}>
            <HeroBackgroundImage backgroundImageLandscape={backgroundImage} optimizeForLCP={optimizeForLCP}/>
            <div className={'pricing-tabs'}>
                <PricingPeriod
                    selected={selectedPeriod}
                    set={setPeriod}
                    periods={periods}/>
                <PricingCurrency
                    prices={prices}
                    selected={selectedCurrency}
                    set={setCurrency}
                />
            </div>
            <CarouselProvider context={{currentIndex: 1, totalItems: cards.length}}>
                <PricingCarousel>
                    {cards.map(pricing => (<PricingCard
                        key={pricing._key}
                        pricePercentage={selectedPeriod?.percentage}
                        priceCurrency={selectedCurrency}
                        {...pricing}/>))}
                </PricingCarousel>
            </CarouselProvider>
            <div className={'observation-text'}>
                {observationText}
            </div>
            <style jsx>{styles}</style>
        </section>
    );
}

ProductPricingSection.propTypes = {
    productName: PropTypes.string,
    pricings:    PropTypes.array,
    periods:     PropTypes.arrayOf(PropTypes.shape({
        default:    PropTypes.bool,
        label:      PropTypes.string,
        percentage: PropTypes.number
    })),
    observationText: PropTypes.string,
    backgroundImage: PropTypes.object,
    index:           PropTypes.number
};

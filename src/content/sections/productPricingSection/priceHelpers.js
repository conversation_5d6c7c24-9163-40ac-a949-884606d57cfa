import _ from 'lodash';

import siteConfig from 'rf-data/siteConfig.json';

import {DOLLAR_CURRENCY_KEY, EURO_CURRENCY_KEY} from './constants';

const {language} = siteConfig;

const euroFormat = Intl.NumberFormat(`${language}-DE`, {
    style:                 'currency',
    currency:              'EUR',
    minimumFractionDigits: 2
});

const usDollarFormat = Intl.NumberFormat(`${language}-US`, {
    style:                 'currency',
    currency:              'USD',
    minimumFractionDigits: 2
});

const replaceDoubleZeroFractionDigits = value => {
    return value.replace(/\D00$/, '');
};

export const currencies = {
    [EURO_CURRENCY_KEY]: {
        label:    'Euro',
        altLabel: '€/EUR',
        format:   value => replaceDoubleZeroFractionDigits(euroFormat.format(value))
    },
    [DOLLAR_CURRENCY_KEY]: {
        label:    'Dollar',
        altLabel: '$/USD',
        format:   value => replaceDoubleZeroFractionDigits(usDollarFormat.format(value))
    }
};

export function getCalculatedPrice(percentage, price) {
    if (!price) {
        return 0;
    }
    if (!percentage) {
        return price;
    }

    return _.round(price * (1 + (percentage / 100)), 2);
}

export function getCurrentPriceAndCurrency(prices, priceCurrency) {
    let currentPrice = _.find(prices, {currency: priceCurrency});

    if (_.isEmpty(currentPrice)) {
        currentPrice = _.find(prices, {currency: EURO_CURRENCY_KEY});
    }

    return currentPrice;
}

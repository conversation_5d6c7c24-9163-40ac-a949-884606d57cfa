import {getCalculatedPrice} from '../priceHelpers';

const percentageTestData = [
    [0, 10, 10],
    [10, 0, 0],
    [10, 10, 11],
    [100, 10, 20],
    [200, 10, 30],
    [15, 5, 5.75],
    [21.5, 27.5, 33.41],
    [30.99, 30.99, 40.59],
    [undefined, 100, 100],
    [10, undefined, 0]
];

describe('priceHelpers', () => {
    test.each(percentageTestData)('calculates and rounds the price based on percentage', (percentage, price, expected) => {
        const result = getCalculatedPrice(percentage, price);
        expect(result).toBe(expected);
    });
});

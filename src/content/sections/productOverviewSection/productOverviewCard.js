/* eslint-disable max-lines */
import _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {Image} from 'rf-components';

import {colors, fontWeights, hSpacing, vSpacing, screenSizes} from 'rf-styles';
import {shouldOpenInNewTab} from 'rf-utils';

const styles = css`
.module-card {
    width: 100%;
    margin: ${vSpacing.m / 2}px ${hSpacing.s}px;
    border-radius: 5px;
    box-shadow: 0px 4px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    text-decoration: none;
    justify-content: center;
    align-items: center;
    font-weight: inherit;
    overflow: hidden;
}

.module-card-bottom {
    width: calc(100% - 2px);
    border-radius: 5px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border: 1px solid rgba(204, 204, 204, 1);
    border-top: none;
}

.big-image {
    display: none;
}

.module-with-ref {
    transition: transform 0.250s;
}

.module-with-ref:hover {
    transform: scale(1.05);
    cursor: pointer;
}

.blue-text {
    color: ${colors.blue}
}

.gray-text {
    color: ${colors.darkGray}
}

.category {
    font-size: 12px;
    padding-top: ${vSpacing.s}px;
    line-height: 14px;
}

.title {
    font-size: 14px;
    line-height: 18px;
    font-weight: ${fontWeights.bold};
    text-align: center;
}

.image-container {
    position: relative;
    height: 140px;
    overflow: hidden;
    transform: scale(1);
}

.center-text {
    text-align: center;
}

.description-container {
    margin: 0px ${hSpacing.s}px ${hSpacing.s}px ${hSpacing.s}px;
}

.description {
    margin-top: ${vSpacing.xs}px;
    font-size: 12px;
    line-height: 14px;
    text-align: center;
}

@media (min-width: ${screenSizes.xxs}px) {
    .title {
        font-size: 16px;
        line-height: 18px;
    }
}

@media (min-width: ${screenSizes.xs}px) {
    .module-card {
        width: calc(50% - 30px);
    }
}

@media (min-width: ${screenSizes.s}px) {
    .small-image {
        display: none;
    }

    .big-image {
        display: flex;
        justify-content: center;
        min-width: 458px;
    }

    .module-card {
        width: calc(25% - 30px);
    }

    .module-with-ref:hover {
        transform: scale(1.1)
    }
}

@media (min-width: ${screenSizes.m}px) {
    .category, .description {
        line-height: 16px;
    }

    .title {
        font-size: 18px;
        line-height: 22px;
    }

    .description-container {
        margin: 0px ${hSpacing.sm}px ${vSpacing.s}px ${hSpacing.sm}px;
    }

    .image-container {
        height: 200px;
    }

    .big-image {
        min-width: 1000px;
    }
}

@media (min-width: ${screenSizes.xl}px) {
    .module-card {
        margin: ${hSpacing.m / 2}px ${hSpacing.m / 2}px;
    }

    .description-container {
        margin: 0px ${hSpacing.xs}px ${vSpacing.s}px ${hSpacing.xs}px;
    }
}
`;

function getTitleClass(isBlueTitle) {
    return isBlueTitle ? 'blue-text' : 'gray-text';
}

export function ProductOverviewCard({title, category, description, bigImage, smallImage, imageAlt, route, url, isBlueTitle}) {
    let link;
    let newTabProps = {};
    let className = '';
    let WrapperElement = 'div';

    if (route?.href || url) {
        link = url || route.href;
        link = link.replace(/\/$/, '');
        className = 'module-with-ref';
        WrapperElement = 'a';
    }

    if (url && shouldOpenInNewTab(url)) {
        newTabProps = {
            target: '_blank',
            rel:    'noreferrer'
        };
    }

    return (
        <WrapperElement className={`module-card ${className}`} href={link} {...newTabProps}>
            <div className={'image-container big-image'}>
                <Image image={bigImage} imageAlt={imageAlt} />
            </div>
            <div className={'image-container small-image'}>
                <Image image={smallImage} imageAlt={imageAlt} layout={'cover'}/>
            </div>
            <div className={'rf-flex-1 module-card-bottom'}>
                <div className={`center-content center-text category ${getTitleClass(isBlueTitle)}`}>
                    {_.toUpper(category)}
                </div>
                <div className={`center-content title ${getTitleClass(isBlueTitle)}`}>
                    {title}
                </div>
                <div className={'rf-flex center-content description-container'}>
                    <p className={'description'}>
                        {description}
                    </p>
                </div>
            </div>
            <style jsx>{styles}</style>
        </WrapperElement>
    );
}

ProductOverviewCard.propTypes = {
    title:       PropTypes.string,
    isBlueTitle: PropTypes.bool,
    bigImage:    PropTypes.object,
    smallImage:  PropTypes.object,
    imageAlt:    PropTypes.string,
    category:    PropTypes.string,
    description: PropTypes.string,
    route:       PropTypes.object,
    url:         PropTypes.string
};

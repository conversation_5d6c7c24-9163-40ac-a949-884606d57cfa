// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ResellerSignupFormRight Renders without errors 1`] = `
<form
  autoComplete="off"
  onSubmit={[Function]}
>
  <div
    className="bp5-card bp5-elevation-0"
  >
    <div
      className="rf-cell"
    >
      <h3
        className="title"
      >
        Test title
      </h3>
      <h5
        className="subtitle"
      >
        Some subtitle
      </h5>
      <style />
      <div
        className="bp5-form-group bp5-text-large "
      >
        <label
          className="bp5-label"
          htmlFor="resellerData.salutation"
        >
          form.field.resellerData.salutation
           
          <span
            className="bp5-text-muted"
          />
        </label>
        <div
          className="bp5-form-content"
        >
          <div
            className="bp5-radio-group"
          >
            <label
              className="bp5-control bp5-radio bp5-inline"
            >
              <input
                checked={false}
                name="Blueprint5.RadioGroup-0"
                onChange={[Function]}
                type="radio"
                value="female"
              />
              <span
                className="bp5-control-indicator"
              />
              form.field.salutationFemale
            </label>
            <label
              className="bp5-control bp5-radio bp5-inline"
            >
              <input
                checked={false}
                name="Blueprint5.RadioGroup-0"
                onChange={[Function]}
                type="radio"
                value="male"
              />
              <span
                className="bp5-control-indicator"
              />
              form.field.salutationMale
            </label>
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group bp5-text-large "
      >
        <label
          className="bp5-label"
          htmlFor="resellerData.firstName"
        >
          form.field.resellerData.firstName
           
          <span
            className="bp5-text-muted"
          />
        </label>
        <div
          className="bp5-form-content"
        >
          <div
            className="bp5-input-group"
          >
            <input
              className="bp5-input"
              id="firstName"
              onChange={[Function]}
              placeholder="form.field.resellerData.firstNamePlaceholder"
              style={
                {
                  "paddingLeft": undefined,
                  "paddingRight": undefined,
                }
              }
              type="text"
            />
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group bp5-text-large "
      >
        <label
          className="bp5-label"
          htmlFor="resellerData.lastName"
        >
          form.field.resellerData.lastName
           
          <span
            className="bp5-text-muted"
          />
        </label>
        <div
          className="bp5-form-content"
        >
          <div
            className="bp5-input-group"
          >
            <input
              className="bp5-input"
              id="lastName"
              onChange={[Function]}
              placeholder="form.field.resellerData.lastNamePlaceholder"
              style={
                {
                  "paddingLeft": undefined,
                  "paddingRight": undefined,
                }
              }
              type="text"
            />
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group bp5-text-large "
      >
        <label
          className="bp5-label"
          htmlFor="resellerData.company"
        >
          form.field.resellerData.company
           
          <span
            className="bp5-text-muted"
          />
        </label>
        <div
          className="bp5-form-content"
        >
          <div
            className="bp5-input-group"
          >
            <input
              className="bp5-input"
              id="company"
              onChange={[Function]}
              placeholder="form.field.resellerData.companyPlaceholder"
              style={
                {
                  "paddingLeft": undefined,
                  "paddingRight": undefined,
                }
              }
              type="text"
            />
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group bp5-text-large "
      >
        <label
          className="bp5-label"
          htmlFor="resellerData.email"
        >
          form.field.resellerData.email
           
          <span
            className="bp5-text-muted"
          />
        </label>
        <div
          className="bp5-form-content"
        >
          <div
            className="bp5-input-group"
          >
            <input
              className="bp5-input"
              id="email"
              onChange={[Function]}
              placeholder="form.field.resellerData.emailPlaceholder"
              style={
                {
                  "paddingLeft": undefined,
                  "paddingRight": undefined,
                }
              }
              type="email"
            />
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group bp5-text-large "
      >
        <label
          className="bp5-label"
          htmlFor="resellerData.phone"
        >
          form.field.resellerData.phone
           
          <span
            className="bp5-text-muted"
          />
        </label>
        <div
          className="bp5-form-content"
        >
          <div
            className="bp5-control-group bp5-fill"
            role="group"
          >
            <div
              aria-controls="listbox-0"
              aria-expanded={false}
              aria-haspopup="listbox"
              className="bp5-popover-target"
              onClick={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              role="combobox"
            >
              <button
                className="bp5-button"
                disabled={false}
                onBlur={[Function]}
                onKeyDown={[Function]}
                onKeyUp={[Function]}
                type="button"
              >
                <span
                  className="bp5-button-text"
                >
                  +1
                </span>
                <span
                  aria-hidden={true}
                  className="bp5-icon bp5-icon-standard bp5-icon-caret-down"
                  data-icon="caret-down"
                />
              </button>
              <style />
            </div>
            <div
              className="bp5-input-group bp5-fill"
            >
              <input
                className="bp5-input"
                onChange={[Function]}
                placeholder="2015550123"
                style={
                  {
                    "paddingLeft": undefined,
                    "paddingRight": undefined,
                  }
                }
                type="tel"
              />
            </div>
          </div>
          <style />
        </div>
      </div>
      <div>
        <div
          className="bp5-form-group"
          style={
            {
              "margin": 0,
            }
          }
        >
          <div
            className="bp5-form-content button-row"
          >
            <div
              className="center-content"
            />
            <button
              className="bp5-button bp5-large bp5-intent-primary"
              disabled={false}
              onBlur={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              type="submit"
            >
              <span
                className="bp5-button-text"
              >
                form.action.submit
              </span>
            </button>
          </div>
        </div>
        <style />
      </div>
      <div
        style={
          {
            "marginTop": "15px",
          }
        }
      >
        form.privacyPolicyText
         
        <a
          href="http://www.privacy-policy.com"
        >
          form.privacyPolicy
        </a>
        .
      </div>
    </div>
  </div>
  <div
    autoFocus={true}
  >
    <div
      className="loader-overlay rf-flex-container"
    >
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
      <div
        className="center-content"
      >
        <img
          alt="company logo icon"
          className="spinner-logo"
          src="/assets/images/spinner-logo.png"
        />
        <img
          alt="company logo text"
          src="/assets/images/spinner-text.png"
        />
      </div>
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
    </div>
    <style />
  </div>
  <style />
</form>
`;

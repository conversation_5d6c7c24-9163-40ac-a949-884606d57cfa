import React from 'react';
import PropTypes from 'prop-types';
import {Classes, FormGroup, NumericInput} from '@blueprintjs/core';
import _ from 'lodash';

import {t} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

function filterNumberInput(stringNumber) {
    const filteredNumber = parseInt(stringNumber.replace(/\D/g, ''));

    return _.isNaN(filteredNumber) ? 0 : filteredNumber;
}

export class NumberInput extends React.Component {
    static contextType = FormContext;

    render() {
        const {fieldName, path, placeholder, showLabel = true, formGroupClassName} = this.props;

        const finalPath = path ? path : fieldName;

        const value = _.get(this.context, `fields.${finalPath}.value`);

        return (
            <FormGroup
                label={showLabel ? t(`form.field.${fieldName}`) : undefined}
                labelFor={fieldName}
                className={[Classes.TEXT_LARGE, formGroupClassName]}>
                <NumericInput
                    fill
                    id={fieldName}
                    min={0}
                    value={value}
                    placeholder={placeholder || t(`form.field.${fieldName}Placeholder`)}
                    onValueChange={
                        (_number, stringNumber) => this.context.updateField({path: finalPath, evt: {target: {value: filterNumberInput(stringNumber)}}})
                    }/>
            </FormGroup>
        );
    }
}

NumberInput.propTypes = {
    fieldName:          PropTypes.string,
    path:               PropTypes.string,
    placeholder:        PropTypes.string,
    showLabel:          PropTypes.bool,
    formGroupClassName: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.string])
};

import React from 'react';

import moment from 'rf-moment-locale';
import {FormContext} from 'rf-form-utils';

import {GoLiveDatePicker} from '../goLiveDatePicker';

describe('GoLiveDatePicker', () => {
    test('Should render without errors', () => {
        const component = render(<GoLiveDatePicker />);

        expect(component).toMatchSnapshot();
    });

    describe('onDateChange', () => {
        let onDateChange;
        const mockContext = {
            updateField: jest.fn()
        };

        beforeAll(() => {
            const component = render(<FormContext.Provider value={mockContext}><GoLiveDatePicker /></FormContext.Provider>);
            const selectInput = component.root.findByProps({id: 'goLiveDate'});

            onDateChange = selectInput.props.onChange;
        });

        beforeEach(jest.clearAllMocks);

        test('Updates goLiveDate field with inserted value when date is valid', () => {
            const value = new Date();

            onDateChange(value);

            expect(mockContext.updateField).toBeCalledWith({fieldName: 'goLiveDate', evt: {target: {value: moment(value).valueOf()}}});
        });

        test('Updates goLiveDate field with null when date is not defined', () => {
            onDateChange();

            expect(mockContext.updateField).toBeCalledWith({fieldName: 'goLiveDate', evt: {target: {value: null}}});
        });

        test('updates field with start of the day if date is before today', () => {
            const value = new Date('2023-01-01');
            onDateChange(value);

            expect(mockContext.updateField).toBeCalledWith({fieldName: 'goLiveDate', evt: {target: {value: moment().startOf('day').valueOf()}}});
        });

        test('caps date to 20 years in the future', () => {
            const value = new Date('2100-11-30 23:59:59');
            onDateChange(value);

            expect(mockContext.updateField).toBeCalledWith({fieldName: 'goLiveDate', evt: {target: {value: moment().add(20, 'y').endOf('y').valueOf()}}});
        });

        test('Updates field if date is within the limit', () => {
            const value = new Date('2043-11-30 23:59:59');
            onDateChange(value);

            expect(mockContext.updateField).toHaveBeenCalled();
        });
    });

    describe('getMomentFormatter', () => {
        let dateFns;

        beforeAll(() => {
            const component = render(<GoLiveDatePicker />);
            const selectInput = component.root.findByProps({id: 'goLiveDate'});

            dateFns = {
                formatDate: selectInput.props.formatDate,
                parseDate:  selectInput.props.parseDate
            };
        });

        test('Parses date', () => {
            const locale = 'en-US';

            expect(dateFns.parseDate('01/12/2023', locale)).toStrictEqual(new Date());
        });

        test('Formats date', () => {
            const locale = 'en-US';

            expect(dateFns.formatDate('01.12.2023', locale)).toStrictEqual('01/12/2023');
        });
    });
});

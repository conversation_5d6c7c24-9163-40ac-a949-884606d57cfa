import React from 'react';

import {MainObjectiveSelector, selectItems} from '../mainObjectiveSelector';
import {FormContext} from 'rf-form-utils';

jest.mock('rf-i18n', () => ({
    t: label => {
        const mockMainObjectives = {
            'form.field.mainObjectiveOption000': 'MO0',
            'form.field.mainObjectiveOption001': 'MO1',
            'form.field.mainObjectiveOption002': 'MO2'
        };

        return mockMainObjectives[label] || label;
    }
}));

describe('mainObjectiveSelector', () => {
    test('renders correctly', () => {
        const component = render(<MainObjectiveSelector/>);

        expect(component).toMatchSnapshot();
    });

    test('renders the selected item correctly', () => {
        const component = render(<MainObjectiveSelector/>);

        const selectInput = component.root.find(el => el.props.onItemSelect);

        act(() => {
            selectInput.props.onItemSelect(selectItems[1]);
        });

        expect(component).toMatchSnapshot();
    });

    test('renders null when item is null', () => {
        const component = render(<MainObjectiveSelector/>);

        const itemRenderer = component.root.find(el => el.props.itemRenderer).props.itemRenderer;

        expect(itemRenderer(null, {})).toEqual(null);
    });

    test('setSelectedItem sets the active item', () => {
        const component = render(<MainObjectiveSelector/>);

        const selectInput = component.root.find(el => el.props.onItemSelect);

        act(() => {
            selectInput.props.onItemSelect(selectItems[1]);
        });

        expect(selectInput.props.activeItem).toEqual(selectItems[1]);
    });

    test('setSelectedItem calls updateField', () => {
        const mockContext = {
            updateField: jest.fn()
        };

        const component = render(<FormContext.Provider value={mockContext}> <MainObjectiveSelector /> </FormContext.Provider>);

        const selectInput = component.root.find(el => el.props?.onItemSelect);

        act(() => {
            selectInput.props.onItemSelect(selectItems[1]);
        });

        expect(mockContext.updateField).toHaveBeenCalledWith({
            fieldName: 'mainObjective',
            evt:       {target: {value: selectItems[1].value}}
        });
    });

    test('setActiveItem sets the active item in the state', () => {
        const component = render(<MainObjectiveSelector/>);

        const selectInput = component.root.find(el => el.props.onActiveItemChange);

        act(() => {
            selectInput.props.onActiveItemChange(selectItems[1]);
        });

        expect(selectInput.props.activeItem).toEqual(selectItems[1]);
    });
});

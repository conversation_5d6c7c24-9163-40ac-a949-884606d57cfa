// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`mainObjectiveSelector renders correctly 1`] = `
[
  <div
    className="bp5-form-group bp5-text-large main-objective-selector"
  >
    <label
      className="bp5-label"
      htmlFor="mainObjective"
    >
      form.field.mainObjective
       
      <span
        className="bp5-text-muted"
      />
    </label>
    <div
      className="bp5-form-content"
    >
      <div
        aria-controls="listbox-0"
        aria-expanded={false}
        aria-haspopup="listbox"
        className="bp5-popover-target"
        onClick={[Function]}
        onKeyDown={[Function]}
        onKeyUp={[Function]}
        role="combobox"
      >
        <button
          className="bp5-button bp5-fill"
          disabled={false}
          onBlur={[Function]}
          onKeyDown={[Function]}
          onKeyUp={[Function]}
          type="button"
        >
          <span
            className="bp5-button-text"
          >
            form.field.chooseAnOption
          </span>
          <span
            aria-hidden={true}
            className="bp5-icon bp5-icon-standard bp5-icon-caret-down"
            data-icon="caret-down"
          />
        </button>
      </div>
    </div>
  </div>,
  <style />,
]
`;

exports[`mainObjectiveSelector renders the selected item correctly 1`] = `
[
  <div
    className="bp5-form-group bp5-text-large main-objective-selector"
  >
    <label
      className="bp5-label"
      htmlFor="mainObjective"
    >
      form.field.mainObjective
       
      <span
        className="bp5-text-muted"
      />
    </label>
    <div
      className="bp5-form-content"
    >
      <div
        aria-controls="listbox-1"
        aria-expanded={false}
        aria-haspopup="listbox"
        className="bp5-popover-target"
        onClick={[Function]}
        onKeyDown={[Function]}
        onKeyUp={[Function]}
        role="combobox"
      >
        <button
          className="bp5-button bp5-fill"
          disabled={false}
          onBlur={[Function]}
          onKeyDown={[Function]}
          onKeyUp={[Function]}
          type="button"
        >
          <span
            className="bp5-button-text"
          >
            MO1
          </span>
          <span
            aria-hidden={true}
            className="bp5-icon bp5-icon-standard bp5-icon-caret-down"
            data-icon="caret-down"
          />
        </button>
      </div>
    </div>
  </div>,
  <style />,
]
`;

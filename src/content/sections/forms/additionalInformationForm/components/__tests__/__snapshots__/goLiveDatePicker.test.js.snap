// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`GoLiveDatePicker Should render without errors 1`] = `
<div
  className="bp5-form-group bp5-text-large"
>
  <label
    className="bp5-label"
    htmlFor="goLiveDate"
  >
    form.field.goLiveDate
     
    <span
      className="bp5-text-muted"
    />
  </label>
  <div
    className="bp5-form-content"
  >
    <div
      className="bp5-input-group bp5-fill bp5-date-input bp5-popover-target"
    >
      <span
        className="bp5-input-left-container"
      >
        <span
          className="bp3-icon"
        >
          <div
            height={16}
            src="/v4/svgs/calendar.svg"
            width="16px"
          />
        </span>
      </span>
      <input
        aria-controls="date-picker-1"
        aria-disabled={false}
        aria-expanded={false}
        aria-haspopup="menu"
        autoComplete="off"
        className="bp5-input"
        disabled={false}
        onBlur={[Function]}
        onChange={[Function]}
        onClick={[Function]}
        onFocus={[Function]}
        onKeyDown={[Function]}
        placeholder="form.field.goLiveDatePlaceholder"
        role="combobox"
        style={
          {
            "paddingLeft": undefined,
            "paddingRight": undefined,
          }
        }
        type="text"
        value="12/01/2023"
      />
      <span
        className="bp5-input-action"
      />
    </div>
  </div>
</div>
`;

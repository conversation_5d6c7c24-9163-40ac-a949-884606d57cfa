import React from 'react';
import PropTypes from 'prop-types';

import {colors} from 'rf-styles';

import {FormLeft} from '../components';

import {AdditionalInfoRight} from './additionalInfoRight';

export function AdditionalInformationForm({sideImageTitle, sideImage, ...restProps}) {
    return (
        <section className={'page-horizontal-padding section-vertical-padding'} style={{backgroundColor: colors.lightBlue}}>
            <div className={'rf-flex-container'}>
                <div className={'rf-flex-6'}>
                    <FormLeft sideImage={sideImage} sideImageTitle={sideImageTitle}/>
                </div>
                <div className={'rf-cell'}/>
                <div className={'rf-cell rf-flex-4'}>
                    <AdditionalInfoRight {...restProps} />
                </div>
                <div className={'rf-cell'}/>
            </div>
        </section>
    );
}

AdditionalInformationForm.propTypes = {
    successURL:     PropTypes.string,
    title:          PropTypes.string,
    subtitle:       PropTypes.string,
    sideImageTitle: PropTypes.string,
    sideImage:      PropTypes.object,
    redirectURL:    PropTypes.string
};

import React from 'react';
import * as _ from 'lodash';
import {AdditionalInfoRight} from '../additionalInfoRight';
import {salesApi} from 'rf-form-utils';
import {navigationUtil, queryStringUtil} from 'rf-utils';

jest.mock('rf-form-utils', () => ({
    ...jest.requireActual('rf-form-utils'),
    salesApi: {
        updateLead: jest.fn()
    }
}));

jest.mock('rf-utils', () => ({
    ...jest.requireActual('rf-utils'),
    navigationUtil: {
        navigateTo: jest.fn()
    },
    queryStringUtil: {
        getFromUrl: jest.fn().mockImplementation(() => ({hashedId: 'hashedId'}))
    }
}));

describe('AdditionalInfoRight', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('Renders without errors', () => {
        const component = render(<AdditionalInfoRight
            title={'Test title'}
            subtitle={'Some subtitle'}
            redirectURL={'http://www.redirect.com'}
            successURL={'http://www.success.com'}
        />);

        expect(component).toMatchSnapshot();
    });

    test('redirects to the redirect url if the hashedId query param is not present in the url', () => {
        queryStringUtil.getFromUrl.mockImplementationOnce(() => {
            return {};
        });

        render(<AdditionalInfoRight redirectURL={'http://www.google.com'}/>);

        expect(navigationUtil.navigateTo).toHaveBeenLastCalledWith('http://www.google.com');
    });

    test('does not redirect to the redirect url if the hashedId query param is present in the url', () => {
        render(<AdditionalInfoRight redirectURL={'http://www.google.com'}/>);

        expect(navigationUtil.navigateTo).not.toHaveBeenCalled();
    });

    test('submits the information that is present in the form when it correctly filled and redirects to successUrl', async() => {
        const component = render(<AdditionalInfoRight successURL={'www.success.com'}/>);

        const formData = {
            fleetSize:                100,
            poolSize:                 200,
            electricVehicleFleetSize: 300,
            goLiveDate:               new Date(),
            description:              'testDescription'
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const currentState = component.root.instance.state;

        expect(currentState.submitting).toBe(true);

        expect(salesApi.updateLead).toHaveBeenLastCalledWith(
            'hashedId',
            expect.objectContaining({...formData, language: 'de'}),
            undefined
        );
        expect(navigationUtil.navigateTo).toHaveBeenCalledWith('www.success.com', {hashedId: 'hashedId'});
    });

    test('Sets form to an error state if exception happens on submit information', async() => {
        const component = render(<AdditionalInfoRight successURL={'www.success.com'}/>);

        const formData = {
            fleetSize:                100,
            poolSize:                 200,
            electricVehicleFleetSize: 300,
            goLiveDate:               new Date(),
            description:              'testDescription'
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        const errorMessage = 'some error message';

        salesApi.updateLead.mockImplementationOnce(() => { throw {message: errorMessage}; });

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const currentState = component.root.instance.state;

        expect(currentState).toEqual(expect.objectContaining({submitError: errorMessage, submitting: false}));
    });
});

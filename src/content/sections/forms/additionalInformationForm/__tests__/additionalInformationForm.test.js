import React from 'react';
import {AdditionalInformationForm} from '../additionalInformationForm';

describe('AdditionalInformationForm', () => {
    test('Renders without errors', () => {
        const component = render(<AdditionalInformationForm
            sideImage={{asset: {_ref: 'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg'}, height: 3000, width: 2000}}
            sideImageTitle={'Side image title'}
            title={'Test title'}
            subtitle={'Some subtitle'}
            redirectURL={'http://www.redirect.com'}
            successURL={'http://www.success.com'}
        />);

        expect(component).toMatchSnapshot();
    });
});

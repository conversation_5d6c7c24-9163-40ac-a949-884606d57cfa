import React, {useContext} from 'react';
import PropTypes from 'prop-types';
import {Classes, FormGroup, Intent} from '@blueprintjs/core';
import {global} from 'styled-jsx/css';
import _ from 'lodash';

import {t} from 'rf-i18n';
import {colors} from 'rf-styles';
import {FormContext} from 'rf-form-utils';

const styles = global`
.bp3-form-group .bp3-radio {
    margin-top: 0;
    margin-bottom: 0;
}

.bp3-form-group.has-error .bp3-control-indicator {
    box-shadow: inset 0 0 0 1px ${colors.errorRed}, inset 0 -1px 0 ${colors.errorRed};
}}`;

export function ValidatedField({children, labelFor, labelPrefix}) {
    const form = useContext(FormContext);
    const formError = _.get(form, `fields.${labelFor}.error`);

    const label = t(`form.field.${labelPrefix ? `${labelPrefix}.` : ''}${labelFor}`);
    const finalLabelFor = `${labelPrefix ? `${labelPrefix}.` : ''}${labelFor}`;

    return (
        <FormGroup
            label={label}
            labelFor={finalLabelFor}
            helperText={formError ? t(formError) : ''}
            className={`${Classes.TEXT_LARGE} ${formError ? 'has-error' : ''}`}
            intent={formError ? Intent.DANGER : Intent.NONE}>
            {children}
            <style jsx global>{styles}</style>
        </FormGroup>
    );
}

ValidatedField.propTypes = {
    children: PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.array
    ]),
    labelFor:    PropTypes.string,
    labelPrefix: PropTypes.string
};

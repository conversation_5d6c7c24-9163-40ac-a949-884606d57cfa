import React, {Component} from 'react';
import {Intent, MenuItem, Button} from '@blueprintjs/core';
import {Select2} from '@blueprintjs/select';
import _ from 'lodash';
import {getName} from 'i18n-iso-countries';

import {language} from 'rf-data/siteConfig.json';
import {t} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

import {phoneCountryList} from './phoneCountryList';
import {itemPredicate, itemRenderer} from './phonePrefixUtils';
import {phonePrefixStyles} from './phonePrefixStyles';

export class PhonePrefixSelect extends Component {
    constructor(props) {
        super(props);

        this.state = {
            defaults: {
                country:     'DE',
                countryName: getName('DE', language),
                callingCode: '49'
            }
        };

        this.onPrefixChange = this.onPrefixChange.bind(this);
    }

    componentDidMount() {
        const browserLocale = navigator.language || navigator.languages[0];
        const country = browserLocale.split('-')[1]?.toUpperCase() || 'DE';

        this.context.updateField({fieldName: 'country', evt: {target: {value: country}}});

        const newDefaults = _.find(phoneCountryList, {country});
        if (newDefaults) {
            this.setState({defaults: newDefaults});
            this.context.updateField({fieldName: 'phonePrefix', evt: {target: {value: newDefaults.callingCode}}});
        }
    }

    onPrefixChange(phonePrefix) {
        if (phonePrefix !== null) {
            this.setState({phonePrefix});
            this.context.updateField({fieldName: 'country', evt: {target: {value: phonePrefix.country}}});
            this.context.updateField({fieldName: 'phonePrefix', evt: {target: {value: phonePrefix.callingCode}}});
        }
    }

    render() {
        const formError = _.get(this.context, 'fields.phone.error');
        const value = this.state.phonePrefix || this.state.defaults;

        return (
            <div>
                <Select2
                    noResults={<MenuItem disabled={true} text={t('option.noResults')}/>}
                    itemsEqual={(a, b) => a.country === b.country}
                    activeItem={value}
                    onActiveItemChange={this.onPrefixChange}
                    items={phoneCountryList}
                    itemPredicate={itemPredicate}
                    itemRenderer={itemRenderer}
                    popoverProps={{
                        portalClassName: 'phone-prefix-popover'
                    }}
                    popoverContentProps={{
                        'aria-label': t('form.field.phonePrefix')
                    }}>
                    <Button
                        intent={formError ? Intent.DANGER : Intent.NONE}
                        text={value?.callingCode ? `+${value.callingCode}` : 'N/A'}
                        rightIcon={'caret-down'}
                        aria-label={t('form.field.phonePrefix')}
                        id={'phonePrefix'}/>
                    <style jsx global>{phonePrefixStyles}</style>
                </Select2>
            </div>
        );
    }
}

PhonePrefixSelect.contextType = FormContext;

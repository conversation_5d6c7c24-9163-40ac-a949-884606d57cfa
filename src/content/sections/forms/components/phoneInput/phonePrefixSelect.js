import React, {Component} from 'react';
import {Intent, MenuItem, Button} from '@blueprintjs/core';
import {Select2} from '@blueprintjs/select';
import _ from 'lodash';
import {global} from 'styled-jsx/css';
import {getName} from 'i18n-iso-countries';

import {language} from 'rf-data/siteConfig.json';
import {t} from 'rf-i18n';
import {fontWeights} from 'rf-styles';
import {FormContext} from 'rf-form-utils';

import {phoneCountryList} from './phoneCountryList';

const styles = global`
.phone-prefix-popover .bp3-menu {
    font-size: 14px;
    max-height: 150px;
    overflow: auto;
}

.phone-prefix-popover .bp3-menu-item {
    font-weight: ${fontWeights.regular};
    font-size: 14px;
}`;

function itemPredicate(searchText, item) {
    return new RegExp(_.escapeRegExp(searchText || ''), 'ig').test(`${item.countryName.toLowerCase()}+${item.callingCode}`);
}

function itemRenderer(item, {handleClick, modifiers}) {
    return (
        <MenuItem
            active={modifiers.active}
            onClick={handleClick}
            text={`${item.countryName} (+${item.callingCode})`}
            key={`${item.countryName} (+${item.callingCode})`}/>
    );
}

export class PhonePrefixSelect extends Component {
    constructor(props) {
        super(props);

        this.state = {
            defaults: {
                country:     'DE',
                countryName: getName('DE', language),
                callingCode: '49'
            }
        };

        this.onPrefixChange = this.onPrefixChange.bind(this);
    }

    componentDidMount() {
        const browserLocale = navigator.language || navigator.languages[0];
        const country = browserLocale.split('-')[1]?.toUpperCase() || 'DE';

        this.context.updateField({fieldName: 'country', evt: {target: {value: country}}});

        const newDefaults = _.find(phoneCountryList, {country});
        if (newDefaults) {
            this.setState({defaults: newDefaults});
            this.context.updateField({fieldName: 'phonePrefix', evt: {target: {value: newDefaults.callingCode}}});
        }
    }

    onPrefixChange(phonePrefix) {
        if (phonePrefix !== null) {
            this.setState({phonePrefix});
            this.context.updateField({fieldName: 'country', evt: {target: {value: phonePrefix.country}}});
            this.context.updateField({fieldName: 'phonePrefix', evt: {target: {value: phonePrefix.callingCode}}});
        }
    }

    render() {
        const formError = _.get(this.context, 'fields.phone.error');
        const value = this.state.phonePrefix || this.state.defaults;

        return (
            <Select2
                noResults={<MenuItem disabled={true} text={t('option.noResults')}/>}
                itemsEqual={(a, b) => a.country === b.country}
                activeItem={value}
                onActiveItemChange={this.onPrefixChange}
                items={phoneCountryList}
                itemPredicate={itemPredicate}
                itemRenderer={itemRenderer}
                popoverProps={{portalClassName: 'phone-prefix-popover'}}>
                <Button
                    intent={formError ? Intent.DANGER : Intent.NONE}
                    text={value?.callingCode ? `+${value.callingCode}` : 'N/A'}
                    rightIcon={'caret-down'}/>
                <style jsx global>{styles}</style>
            </Select2>
        );
    }
}

PhonePrefixSelect.contextType = FormContext;

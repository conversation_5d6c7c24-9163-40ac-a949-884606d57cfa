import React, {useContext} from 'react';
import PropTypes from 'prop-types';
import {ControlGroup, InputGroup, Intent} from '@blueprintjs/core';
import _ from 'lodash';
import {getExampleNumber} from 'libphonenumber-js';
import examples from 'libphonenumber-js/examples.mobile';

import {t} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

import {ValidatedField} from '../validatedField';

import {PhonePrefixSelect} from './phonePrefixSelect';

export function PhoneInput({labelPrefix}) {
    const form = useContext(FormContext);
    const formError = _.get(form, 'fields.phone.error');
    const phoneNumber = _.get(form, 'fields.phone.value');
    const country = _.get(form, 'fields.country.value');
    const exampleNumber = _.get(getExampleNumber(country, examples), 'nationalNumber');

    return (
        <ValidatedField labelFor={'phone'} labelPrefix={labelPrefix}>
            <ControlGroup fill={true} vertical={false}>
                <PhonePrefixSelect/>
                <InputGroup
                    type={'tel'}
                    placeholder={exampleNumber || t('form.field.phonePlaceholder')}
                    defaultValue={phoneNumber}
                    onChange={evt => form.updateField({fieldName: 'phone', evt})}
                    intent={formError ? Intent.DANGER : Intent.NONE}
                    fill={true}
                />
            </ControlGroup>
        </ValidatedField>
    );
}

PhoneInput.propTypes = {
    labelPrefix: PropTypes.string
};

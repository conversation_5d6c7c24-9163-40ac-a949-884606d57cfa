import React from 'react';
import {MenuItem} from '@blueprintjs/core';
import _ from 'lodash';

export function itemPredicate(searchText, item) {
    return new RegExp(_.escapeRegExp(searchText || ''), 'ig').test(`${item.countryName.toLowerCase()}+${item.callingCode}`);
}

export function itemRenderer(item, {handleClick, modifiers}) {
    return (
        <MenuItem
            active={modifiers.active}
            onClick={handleClick}
            text={`${item.countryName} (+${item.callingCode})`}
            key={`${item.countryName} (+${item.callingCode})`}/>
    );
}

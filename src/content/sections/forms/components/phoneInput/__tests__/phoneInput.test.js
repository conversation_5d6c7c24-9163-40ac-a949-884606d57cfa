import React from 'react';
import {Intent} from '@blueprintjs/core';

describe('PhoneInput', () => {
    const mockUpdateField = jest.fn();
    let mockContext = {
        fields:      {},
        updateField: mockUpdateField
    };

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('Should render without errors', () => {
        const {PhoneInput} = require('../phoneInput');

        const component = render(<PhoneInput />);

        expect(component).toMatchSnapshot();
    });

    test('Initializes with context value', () => {
        const {PhoneInput} = require('../phoneInput');

        mockContext.fields.phone = {value: '123456789'};

        const component = render(<PhoneInput />);

        const phoneField = component.root.findByProps({defaultValue: '123456789'});

        expect(phoneField).toBeTruthy();
    });

    test('Changes intent to danger when field has error', () => {
        const {PhoneInput} = require('../phoneInput');

        mockContext.fields.phone = {value: '123456789', error: 'An error message'};

        const component = render(<PhoneInput />);

        const phoneField = component.root.findByProps({intent: Intent.DANGER});

        expect(phoneField).toBeTruthy();
    });

    test('Updates field on value change', () => {
        const {PhoneInput} = require('../phoneInput');

        mockContext.fields.phone = {value: '123456789'};

        const component = render(<PhoneInput />);

        const phoneField = component.root.findByProps({defaultValue: '123456789'});

        phoneField.props.onChange({target: {value: '123123123'}});

        expect(mockUpdateField).toBeCalledWith({fieldName: 'phone', evt: {target: {value: '123123123'}}});
    });
});

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PhonePrefixSelect Should render without errors 1`] = `
<div
  aria-controls="listbox-0"
  aria-expanded={false}
  aria-haspopup="listbox"
  className="bp5-popover-target"
  onClick={[Function]}
  onKeyDown={[Function]}
  onKeyUp={[Function]}
  role="combobox"
>
  <button
    className="bp5-button"
    disabled={false}
    onBlur={[Function]}
    onKeyDown={[Function]}
    onKeyUp={[Function]}
    type="button"
  >
    <span
      className="bp5-button-text"
    >
      +1
    </span>
    <span
      aria-hidden={true}
      className="bp5-icon bp5-icon-standard bp5-icon-caret-down"
      data-icon="caret-down"
    />
  </button>
  <style />
</div>
`;

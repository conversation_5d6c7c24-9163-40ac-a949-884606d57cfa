// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PhoneInput Should render without errors 1`] = `
<div
  className="bp5-form-group bp5-text-large "
>
  <label
    className="bp5-label"
    htmlFor="phone"
  >
    form.field.phone
     
    <span
      className="bp5-text-muted"
    />
  </label>
  <div
    className="bp5-form-content"
  >
    <div
      className="bp5-control-group bp5-fill"
      role="group"
    >
      <div
        aria-controls="listbox-0"
        aria-expanded={false}
        aria-haspopup="listbox"
        className="bp5-popover-target"
        onClick={[Function]}
        onKeyDown={[Function]}
        onKeyUp={[Function]}
        role="combobox"
      >
        <button
          className="bp5-button"
          disabled={false}
          onBlur={[Function]}
          onKeyDown={[Function]}
          onKeyUp={[Function]}
          type="button"
        >
          <span
            className="bp5-button-text"
          >
            +1
          </span>
          <span
            aria-hidden={true}
            className="bp5-icon bp5-icon-standard bp5-icon-caret-down"
            data-icon="caret-down"
          />
        </button>
        <style />
      </div>
      <div
        className="bp5-input-group bp5-fill"
      >
        <input
          className="bp5-input"
          onChange={[Function]}
          placeholder="form.field.phonePlaceholder"
          style={
            {
              "paddingLeft": undefined,
              "paddingRight": undefined,
            }
          }
          type="tel"
        />
      </div>
    </div>
    <style />
  </div>
</div>
`;

import React from 'react';
import PropTypes from 'prop-types';
import {Card} from '@blueprintjs/core';
import {global} from 'styled-jsx/css';
import '@blueprintjs/core/lib/css/blueprint.css';
import '@blueprintjs/icons/lib/css/blueprint-icons.css';
import '@blueprintjs/select/lib/css/blueprint-select.css';

import {fontWeights} from 'rf-styles';

import {Loader} from './loader';

const styles = global`
.bp3-form-group .bp3-label {
    font-weight: ${fontWeights.semiBold}
}

.bp3-popover-content .bp3-menu-item {
    margin: 0px;
}

.bp3-popover-content .bp3-menu-item > div {
    padding: 5px 0 5px 0;
    font-weight: normal;
}
`;

export function Form({children, onSubmit}) {
    return (
        <form autoComplete={'off'} onSubmit={onSubmit}>
            <Card>
                <div className={'rf-cell'}>
                    {children}
                </div>
            </Card>
            <Loader/>
            <style jsx global>{styles}</style>
        </form>
    );
}

Form.propTypes = {
    children: PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.array
    ]),
    onSubmit: PropTypes.any
};

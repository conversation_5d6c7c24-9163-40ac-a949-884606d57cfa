import React, {useContext} from 'react';
import PropTypes from 'prop-types';
import {Classes, InputGroup} from '@blueprintjs/core';
import _ from 'lodash';

import {t} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

import {ValidatedField} from './validatedField';

export function TextInput({fieldName, labelPrefix = '', type = 'text'}) {
    const form = useContext(FormContext);
    const formError = _.get(form, `fields.${labelPrefix ? `${labelPrefix}.` : ''}${fieldName}.error`);
    const placeholder = t(`form.field.${labelPrefix ? `${labelPrefix}.` : ''}${fieldName}Placeholder`);

    return (
        <ValidatedField labelFor={fieldName} labelPrefix={labelPrefix}>
            <InputGroup
                type={type}
                id={fieldName}
                defaultValue={_.get(form, `fields.${fieldName}.value`)}
                placeholder={placeholder}
                onChange={evt => form.updateField({fieldName, evt})}
                className={formError ? Classes.INTENT_DANGER : ''}/>
        </ValidatedField>
    );
}

TextInput.propTypes = {
    fieldName:   PropTypes.string,
    labelPrefix: PropTypes.string,
    type:        PropTypes.string
};

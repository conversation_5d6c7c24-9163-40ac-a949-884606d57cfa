import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {fontWeights, colors} from 'rf-styles';

const styles = css`
.title {
    color: ${colors.darkGray};
    font-weight: ${fontWeights.bold};
    text-align: center;
    font-size: 25px;
    margin: 0;
}

.subtitle {
    text-align: center;
    margin: 20px 0;
    font-size: 20px;
    line-height: 25px;
}`;

export function FormHeader({title, subtitle}) {
    return (
        <>
            <h3 className={'title'}>{title}</h3>
            <h5 className={'subtitle'}>{subtitle}</h5>
            <style jsx>{styles}</style>
        </>
    );
}

FormHeader.propTypes = {
    title:    PropTypes.string,
    subtitle: PropTypes.string
};

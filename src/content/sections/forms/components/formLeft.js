import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, screenSizes} from 'rf-styles';
import {Image} from 'rf-components';

const styles = css`
.side-container {
    display: none;
}
@media (min-width: ${screenSizes.l}px) { .side-container {
    display: flex;
    flex-direction: column;
}

.form-title {
    margin-top: 0;
    color: ${colors.white};
}`;

export function FormLeft({sideImage, sideImageTitle, sideImageAlt}) {
    return (
        <div className={'rf-cell side-container'}>
            {sideImageTitle && <h1 className={'h2 form-title'}>{sideImageTitle}</h1>}
            <Image image={sideImage} imageAlt={sideImageAlt}/>
            <style jsx>{styles}</style>
        </div>
    );
}

FormLeft.propTypes = {
    sideImageTitle: PropTypes.string,
    sideImageAlt:   PropTypes.string,
    sideImage:      PropTypes.object
};

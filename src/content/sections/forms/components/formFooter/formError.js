import React, {useContext} from 'react';
import {FormGroup, Colors} from '@blueprintjs/core';

import {T} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

export function FormError() {
    const form = useContext(FormContext);

    if (!form.submitError) {
        return null;
    }

    return (
        <FormGroup>
            {form.submitError && <T style={{color: Colors.RED2}}>form.error.formSubmission</T>}
        </FormGroup>
    );
}

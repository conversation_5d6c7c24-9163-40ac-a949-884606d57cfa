import React from 'react';
import PropTypes from 'prop-types';
import {global} from 'styled-jsx/css';
import {FormGroup} from '@blueprintjs/core';

import {StepCircles} from './stepCircles';
import {FormSubmitButton} from './formSubmitButton';
import {FormError} from './formError';

const styles = global`
.button-row {
    display: flex;
    justify-content: left;
}`;

export function FormFooter({pages, currentPage, submitButtonLabel, submitButtonIcon}) {
    return (
        <div>
            <FormError/>
            <FormGroup style={{margin: 0}} contentClassName={'button-row'}>
                <StepCircles pages={pages} currentPage={currentPage}/>
                <FormSubmitButton label={submitButtonLabel} icon={submitButtonIcon}/>
            </FormGroup>
            <style jsx global>{styles}</style>
        </div>
    );
}

FormFooter.propTypes = {
    pages:             PropTypes.number,
    currentPage:       PropTypes.number,
    submitButtonLabel: PropTypes.string,
    submitButtonIcon:  PropTypes.string
};

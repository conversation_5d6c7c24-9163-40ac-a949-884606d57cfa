// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FormSubmitButton Renders without errors 1`] = `
<button
  className="bp5-button bp5-large bp5-intent-primary"
  disabled={false}
  onBlur={[Function]}
  onKeyDown={[Function]}
  onKeyUp={[Function]}
  type="submit"
>
  <span
    className="bp5-button-text"
  >
    this is a label
  </span>
  <span
    aria-hidden={true}
    className="bp5-icon bp5-icon-standard bp5-icon-some icon"
    data-icon="some icon"
  />
</button>
`;

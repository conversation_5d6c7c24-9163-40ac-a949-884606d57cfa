import React from 'react';

describe('StepCircles', () => {
    test('Renders correct number of circles', () => {
        const {StepCircles, Circle} = require('../stepCircles');

        const component = render(<StepCircles
            pages={5}
            currentPage={1}
        />);

        const circles = component.root.findAllByType(Circle);

        expect(circles).toHaveLength(5);
    });

    test('Sets correct selected circle', () => {
        const {StepCircles, Circle} = require('../stepCircles');

        const component = render(<StepCircles
            pages={3}
            currentPage={1}
        />);

        const circles = component.root.findAllByType(Circle);

        expect(circles[0].props.selected).toBe(false);
        expect(circles[1].props.selected).toBe(true);
        expect(circles[2].props.selected).toBe(false);
    });

    test('Returns null if no pages', () => {
        const {StepCircles} = require('../stepCircles');

        const component = render(<StepCircles
            pages={0}
            currentPage={1}
        />);

        expect(component.toJSON()).toBe(null);
    });
});

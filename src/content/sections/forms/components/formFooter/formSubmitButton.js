import React from 'react';
import PropTypes from 'prop-types';
import {Button, Intent} from '@blueprintjs/core';

import {T} from 'rf-i18n';

export function FormSubmitButton({label, icon}) {
    return (
        <Button type={'submit'} intent={Intent.PRIMARY} large rightIcon={icon}>
            <T>{label}</T>
        </Button>
    );
}

FormSubmitButton.propTypes = {
    label: PropTypes.string,
    icon:  PropTypes.string
};

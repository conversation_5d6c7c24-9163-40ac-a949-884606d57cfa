import React from 'react';

describe('Loader', () => {
    let mockContext = {};

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));
    });

    test('Renders without errors', () => {
        const {Loader} = require('../loader');

        mockContext.submitting = true;

        const component = render(<Loader />);

        expect(component.root.findByProps({isOpen: true})).toBeTruthy();
        expect(component).toMatchSnapshot();
    });

    test('Sets isOpen to false when not submitting', () => {
        const {Loader} = require('../loader');

        mockContext.submitting = false;

        const component = render(<Loader />);

        expect(component.root.findByProps({isOpen: false})).toBeTruthy();
    });
});

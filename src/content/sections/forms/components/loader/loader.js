import React, {useContext} from 'react';
import {Overlay} from '@blueprintjs/core';
import css from 'styled-jsx/css';

import {FormContext} from 'rf-form-utils';

const styles = css`
.loader-overlay {
    width: 100%;
    height: 100%;
}

@keyframes logo-rotation {
    from { transform: rotate(0); }    
    to { transform: rotate(360deg); }
}

.spinner-logo {
    animation: 1s logo-rotation infinite;
}`;

export function Loader() {
    const form = useContext(FormContext);

    return (
        <Overlay isOpen={form.submitting} autoFocus={true}>
            <div className={'loader-overlay rf-flex-container'}>
                <div style={{flex: 1}}/>
                <div className={'center-content'}>
                    <img className={'spinner-logo'} alt={'company logo icon'} src={'/assets/images/spinner-logo.png'}/>
                    <img src={'/assets/images/spinner-text.png'} alt={'company logo text'}/>
                </div>
                <div style={{flex: 1}}/>
            </div>
            <style jsx>{styles}</style>
        </Overlay>
    );
}

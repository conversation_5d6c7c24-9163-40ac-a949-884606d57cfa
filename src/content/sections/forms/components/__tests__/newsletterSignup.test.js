import React from 'react';

describe('PhoneInput', () => {
    const mockUpdateField = jest.fn();
    let mockContext = {
        fields:      {},
        updateField: mockUpdateField
    };

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('Should render without errors', () => {
        const {NewsletterSignup} = require('../newsletterSignup');

        const component = render(<NewsletterSignup />);

        expect(component).toMatchSnapshot();
    });

    test('Initializes with context value', () => {
        const {NewsletterSignup} = require('../newsletterSignup');

        mockContext.fields.subscribeToNewsletter = {value: true};

        const component = render(<NewsletterSignup />);

        const checkbox = component.root.findByProps({id: 'subscribeToNewsletter'});

        expect(checkbox.props.defaultChecked).toBe(true);
    });

    test('Updates field on value change', () => {
        const {NewsletterSignup} = require('../newsletterSignup');

        mockContext.fields.subscribeToNewsletter = {value: true};

        const component = render(<NewsletterSignup />);

        const checkbox = component.root.findByProps({id: 'subscribeToNewsletter'});

        checkbox.props.onChange({target: {value: false}});

        expect(mockUpdateField).toBeCalledWith({fieldName: 'subscribeToNewsletter', evt: {target: {value: false}}});
    });
});

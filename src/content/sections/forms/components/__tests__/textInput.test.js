import React from 'react';
import {Classes} from '@blueprintjs/core';

describe('TextInput', () => {
    const mockUpdateField = jest.fn();
    let mockContext = {
        fields:      {},
        updateField: mockUpdateField
    };

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('Should render without errors', () => {
        const {TextInput} = require('../textInput');

        const component = render(<TextInput fieldName={'textInput'}/>);

        expect(component).toMatchSnapshot();
    });

    test('Initializes with context value', () => {
        const {TextInput} = require('../textInput');

        mockContext.fields.textInput = {value: 'huehuebr'};

        const component = render(<TextInput fieldName={'textInput'}/>);

        const textField = component.root.findByProps({defaultValue: 'huehuebr'});

        expect(textField).toBeTruthy();
    });

    test('Changes intent to danger when field has error', () => {
        const {TextInput} = require('../textInput');

        mockContext.fields.textInput = {value: 'huehuebr', error: 'BRs are not allowed'};

        const component = render(<TextInput fieldName={'textInput'}/>);

        const textField = component.root.findByProps({className: Classes.INTENT_DANGER});

        expect(textField).toBeTruthy();
    });

    test('Updates field on value change', () => {
        const {TextInput} = require('../textInput');
        const {InputGroup} = require('@blueprintjs/core');

        const component = render(<TextInput fieldName={'textInput'}/>);

        const textField = component.root.findByType(InputGroup);

        textField.props.onChange({target: {value: 'test test test'}});

        expect(mockUpdateField).toBeCalledWith({fieldName: 'textInput', evt: {target: {value: 'test test test'}}});
    });
});

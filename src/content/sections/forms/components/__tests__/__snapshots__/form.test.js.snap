// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Form Renders without errors 1`] = `
<form
  autoComplete="off"
  onSubmit={[Function]}
>
  <div
    className="bp5-card bp5-elevation-0"
  >
    <div
      className="rf-cell"
    >
      <span
        id="child1"
      />
      <span
        id="child2"
      />
    </div>
  </div>
  <div
    autoFocus={true}
    isOpen={false}
  >
    <div
      className="loader-overlay rf-flex-container"
    >
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
      <div
        className="center-content"
      >
        <img
          alt="company logo icon"
          className="spinner-logo"
          src="/assets/images/spinner-logo.png"
        />
        <img
          alt="company logo text"
          src="/assets/images/spinner-text.png"
        />
      </div>
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
    </div>
    <style />
  </div>
  <style />
</form>
`;

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextInput Should render without errors 1`] = `
<div
  className="bp5-form-group bp5-text-large "
>
  <label
    className="bp5-label"
    htmlFor="textInput"
  >
    form.field.textInput
     
    <span
      className="bp5-text-muted"
    />
  </label>
  <div
    className="bp5-form-content"
  >
    <div
      className="bp5-input-group"
    >
      <input
        className="bp5-input"
        id="textInput"
        onChange={[Function]}
        placeholder="form.field.textInputPlaceholder"
        style={
          {
            "paddingLeft": undefined,
            "paddingRight": undefined,
          }
        }
        type="text"
      />
    </div>
    <style />
  </div>
</div>
`;

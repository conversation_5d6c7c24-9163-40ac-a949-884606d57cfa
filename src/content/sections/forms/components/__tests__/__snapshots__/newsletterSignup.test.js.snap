// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PhoneInput Should render without errors 1`] = `
<div
  className="bp5-form-group"
>
  <div
    className="bp5-form-content"
  >
    <label
      className="bp5-control bp5-checkbox bp5-large bp5-text-large"
    >
      <input
        id="subscribeToNewsletter"
        onChange={[Function]}
        type="checkbox"
      />
      <span
        className="bp5-control-indicator"
      />
      form.field.subscribeToNewsletter
    </label>
  </div>
</div>
`;

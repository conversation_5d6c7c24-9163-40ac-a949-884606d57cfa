import React from 'react';
import {Intent} from '@blueprintjs/core';

describe('PhoneInput', () => {
    const mockUpdateField = jest.fn();
    let mockContext = {
        fields:      {},
        updateField: mockUpdateField
    };

    beforeAll(() => {
        jest.mock('react', () => ({
            ...jest.requireActual('react'),
            useContext: () => mockContext
        }));

        jest.mock('rf-i18n', () => ({
            t: v => v
        }));
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('Should render without errors', () => {
        const {ValidatedField} = require('../validatedField');

        const component = render(<ValidatedField labelFor={'myField'}>
            <span>does this child get rendered?</span>
        </ValidatedField>);

        expect(component).toMatchSnapshot();
    });

    test('Shows error message on helper text and changes intent', () => {
        const {ValidatedField} = require('../validatedField');

        mockContext.fields.myField = {error: 'huehuebr'};

        const component = render(<ValidatedField labelFor={'myField'}/>);

        const textField = component.root.findByProps({helperText: 'huehuebr', intent: Intent.DANGER});

        expect(textField).toBeTruthy();
    });
});

import React, {useContext} from 'react';
import {Checkbox, Classes, FormGroup} from '@blueprintjs/core';
import _ from 'lodash';

import {T} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

export function NewsletterSignup() {
    const form = useContext(FormContext);

    return (
        <FormGroup>
            <Checkbox
                large
                id={'subscribeToNewsletter'}
                defaultChecked={_.get(form, 'fields.subscribeToNewsletter.value')}
                onChange={evt => form.updateField({fieldName: 'subscribeToNewsletter', evt})}
                className={`${Classes.TEXT_LARGE}`}>
                <T>form.field.subscribeToNewsletter</T>
            </Checkbox>
        </FormGroup>
    );
}

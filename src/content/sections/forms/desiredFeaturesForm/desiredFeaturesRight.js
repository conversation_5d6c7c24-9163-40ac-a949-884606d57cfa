import React, {Component} from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

import {t} from 'rf-i18n';

import {navigationUtil, queryStringUtil} from 'rf-utils';
import {salesApi, FormContext, updateField} from 'rf-form-utils';

import {FormFooter, FormHeader, Form} from '../components';

import {FeatureGroup} from './components';

export const featureGroups = [
    {
        name:                      'productsForFleets',
        features:                  ['corporateCarSharing', 'fleetManagement', 'logbook', 'driverLicenseCheck', 'bikeSharing'],
        placeholderLabelGenerator: feature => t(`form.field.generic${feature === 'driverLicenseCheck' ? 'People' : 'Vehicle'}CountPlaceholder`)
    },
    {
        name:                      'productsForOperators',
        features:                  ['cityCarSharing', 'estateCarSharing', 'carRental'],
        placeholderLabelGenerator: () => t('form.field.genericVehicleCountPlaceholder')
    },
    {
        name:                      'fleetHardware',
        features:                  ['obdDataStick', 'electronicKeyCabinet', 'carSharingKit'],
        placeholderLabelGenerator: () => t('form.field.genericVehicleCountPlaceholder')
    }
];

function getProcessedForm(form) {
    const desiredFeatures = _.transform(form.fields, (acc, field, fieldName) => {
        if (field.value) {
            acc.push(fieldName);
        }
    }, []);

    let desiredFeaturesInfo = _.get(form, 'fields.extended.desiredFeatures', {});

    Object.keys(desiredFeaturesInfo).forEach(feature => {
        if (!desiredFeatures.includes(feature)) {
            delete desiredFeaturesInfo[feature];
        }
    });

    desiredFeatures.forEach(feature => {
        const sizeValue = _.get(form, `fields.extended.desiredFeatures.${feature}.size.value`) || 0;

        _.set(form, `fields.extended.desiredFeatures.${feature}.size`, sizeValue);
        _.set(form, `fields.extended.desiredFeatures.${feature}.checked`, true);
    });

    const hasHardwareInterest = _.some(desiredFeatures, feature => featureGroups[2].features.includes(feature));

    return {
        desiredFeatures,
        extended: {...form.fields.extended, hasHardwareInterest}
    };
}

export class DesiredFeaturesRight extends Component {
    static propTypes = {
        title:         PropTypes.string,
        subtitle:      PropTypes.string,
        successURL:    PropTypes.string,
        redirectURL:   PropTypes.string,
        salesEndpoint: PropTypes.string
    };

    constructor(props) {
        super(props);

        this.state = {fields: {}};
    }

    componentDidMount() {
        const {redirectURL} = this.props;

        const {hashedId} = queryStringUtil.getFromUrl();
        if (!hashedId) {
            navigationUtil.navigateTo(redirectURL);
        }

        this.setState({hashedId});
    }

    async onSubmit(evt) {
        evt.preventDefault();

        const processedForm = getProcessedForm(_.cloneDeep(this.state));

        this.setState({submitError: undefined, submitting: true});

        try {
            await salesApi.updateLead(this.state.hashedId, processedForm, this.props.salesEndpoint);
        } catch (error) {
            this.setState({submitError: _.get(error, 'message', error), submitting: false});

            return;
        }

        navigationUtil.navigateTo(this.props.successURL);
    }

    render() {
        const {title, subtitle} = this.props;

        return (
            <FormContext.Provider value={{...this.state, updateField: updateField.bind(this)}}>
                <Form onSubmit={this.onSubmit.bind(this)}>
                    <FormHeader title={title} subtitle={subtitle}/>

                    {featureGroups.map(featureGroup => <FeatureGroup {...featureGroup} key={featureGroup.name}/>)}

                    <FormFooter pages={2} currentPage={1} submitButtonLabel={'form.action.send'}/>
                </Form>
            </FormContext.Provider>
        );
    }
}

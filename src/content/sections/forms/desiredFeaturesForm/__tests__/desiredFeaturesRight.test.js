import React from 'react';
import {DesiredFeaturesRight, featureGroups} from '../desiredFeaturesRight';
import {FeatureGroup} from '../components/featureGroup';
import {salesApi} from 'rf-form-utils';
import {navigationUtil, queryStringUtil} from 'rf-utils';

jest.mock('rf-form-utils', () => ({
    ...jest.requireActual('rf-form-utils'),
    salesApi: {
        updateLead: jest.fn()
    }
}));

jest.mock('rf-utils', () => ({
    ...jest.requireActual('rf-utils'),
    navigationUtil: {
        navigateTo: jest.fn()
    },
    queryStringUtil: {
        getFromUrl: jest.fn().mockImplementation(() => ({hashedId: 'hashedId'}))
    }
}));

describe('DesiredFeaturesRight', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('Renders without errors', () => {
        const component = render(<DesiredFeaturesRight
            title={'Test title'}
            subtitle={'Some subtitle'}
            redirectURL={'http://www.redirect.com'}
            successURL={'http://www.success.com'}
        />);

        expect(component).toMatchSnapshot();
    });

    test('Renders all feature groups correctly', () => {
        const component = render(<DesiredFeaturesRight />);

        const featureComponents = component.root.findAllByType(FeatureGroup);
        featureGroups.forEach((feature, i) => {
            const featureComponent = featureComponents[i];
            const props = featureComponent.props;

            expect(props).toEqual(feature);
        });
    });

    test('Renders feature groups correctly if the matching checkbox is checked (size inputs)', () => {
        const component = render(<DesiredFeaturesRight />);

        component.root.instance.setState({fields: {productsForFleets: {value: true}}});

        expect(component).toMatchSnapshot();
    });

    test('redirects to the redirect url if the hashedId query param is not present in the url', () => {
        queryStringUtil.getFromUrl.mockImplementationOnce(() => {
            return {};
        });

        render(<DesiredFeaturesRight redirectURL={'http://www.google.com'}/>);

        expect(navigationUtil.navigateTo).toHaveBeenLastCalledWith('http://www.google.com');
    });

    test('does not redirect to the redirect url if the hashedId query param is present in the url', () => {
        render(<DesiredFeaturesRight redirectURL={'http://www.google.com'}/>);

        expect(navigationUtil.navigateTo).not.toHaveBeenCalled();
    });

    test('Submits the correct desired features', async() => {
        const component = render(<DesiredFeaturesRight successURL={'www.success.com'}/>);

        const formData = {
            feat1: true,
            feat2: false,
            feat3: true,
            feat4: false
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const currentState = component.root.instance.state;

        expect(currentState.submitting).toBe(true);

        expect(salesApi.updateLead).toHaveBeenLastCalledWith(
            'hashedId',
            {
                desiredFeatures: ['feat1', 'feat3'],
                extended:        {
                    desiredFeatures: {
                        feat1: {
                            checked: true,
                            size:    0
                        },
                        feat3: {
                            checked: true,
                            size:    0
                        }
                    },
                    hasHardwareInterest: false
                }
            },
            undefined
        );
    });

    test('Sets form to an error state if exception happens on submit information', async() => {
        const component = render(<DesiredFeaturesRight successURL={'www.success.com'}/>);

        const formData = {
            feat1: true,
            feat2: false,
            feat3: true,
            feat4: false
        };

        component.root.instance.setState({fields: _.mapValues(formData, value => ({value}))});

        const errorMessage = 'some error message';

        salesApi.updateLead.mockImplementationOnce(() => { throw {message: errorMessage}; });

        await component.root.instance.onSubmit({preventDefault: () => {}});

        const currentState = component.root.instance.state;

        expect(currentState).toEqual(expect.objectContaining({submitError: errorMessage, submitting: false}));
    });

    test('getProcessedForm correctly purges the size value of a feature when its checkbox is unset', async() => {
        const component = render(<DesiredFeaturesRight successURL={'www.success.com'}/>);

        component.root.instance.setState({
            hashedId: 'hashedId',
            fields:   {
                fleetManagement: {
                    value: true
                },
                corporateCarSharing: {
                    value: false
                },
                extended: {
                    desiredFeatures: {
                        corporateCarSharing: {
                            size: 10
                        }
                    }
                }
            }
        });

        await component.root.instance.onSubmit({preventDefault: () => {}});

        expect(salesApi.updateLead).toHaveBeenCalledWith('hashedId', {
            desiredFeatures: ['fleetManagement'],
            extended:        {desiredFeatures: {
                fleetManagement: {
                    checked: true,
                    size:    0
                }
            }, hasHardwareInterest: false}
        }, undefined);
    });

    test('getProcessedForm correctly does not purge the size value of a feature when its checkbox is set', async() => {
        const component = render(<DesiredFeaturesRight successURL={'www.success.com'}/>);

        component.root.instance.setState({
            hashedId: 'hashedId',
            fields:   {
                fleetManagement: {
                    value: true
                },
                corporateCarSharing: {
                    value: true
                },
                extended: {
                    desiredFeatures: {
                        corporateCarSharing: {
                            size: {
                                value: 10
                            }
                        }
                    }
                }
            }
        });

        await component.root.instance.onSubmit({preventDefault: () => {}});

        expect(salesApi.updateLead).toHaveBeenCalledWith('hashedId', {
            desiredFeatures: ['fleetManagement', 'corporateCarSharing'],
            extended:        {
                desiredFeatures:     {fleetManagement: {size: 0, checked: true}, corporateCarSharing: {size: 10, checked: true}},
                hasHardwareInterest: false
            }
        }, undefined);
    });

    test('getProcessedForm sets hasHardwareInterest to true when hardware feature is present', async() => {
        const component = render(<DesiredFeaturesRight successURL={'www.success.com'}/>);

        component.root.instance.setState({
            hashedId: 'hashedId',
            fields:   {
                obdDataStick: {
                    value: true
                },
                extended: {
                    desiredFeatures: {
                        obdDataStick: {
                            size: {value: 10}
                        }
                    }
                }
            }
        });

        await component.root.instance.onSubmit({preventDefault: () => {}});

        expect(salesApi.updateLead).toHaveBeenCalledWith('hashedId', {
            desiredFeatures: ['obdDataStick'],
            extended:        {
                desiredFeatures:     {obdDataStick: {size: 10, checked: true}},
                hasHardwareInterest: true
            }
        }, undefined);
    });
});

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DesiredFeaturesRight Renders feature groups correctly if the matching checkbox is checked (size inputs) 1`] = `
<form
  autoComplete="off"
  onSubmit={[Function]}
>
  <div
    className="bp5-card bp5-elevation-0"
  >
    <div
      className="rf-cell"
    >
      <h3
        className="title"
      />
      <h5
        className="subtitle"
      />
      <style />
      <div
        className="bp5-form-group"
      >
        <div
          className="bp5-form-content"
        >
          <div
            className="feature-group-header"
          >
            <h4
              className="feature-group-title-no-icon"
            >
              form.featureGroup.productsForFleets
            </h4>
          </div>
          <hr
            className="feature-group-separator"
          />
          <div
            className="feature-group"
          >
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="corporateCarSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.corporateCarSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="corporateCarSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-22"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-22"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="fleetManagement"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.fleetManagement
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="fleetManagementSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-23"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-23"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="logbook"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.logbook
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="logbookSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-24"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-24"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="driverLicenseCheck"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.driverLicenseCheck
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="driverLicenseCheckSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericPeopleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-25"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-25"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="bikeSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.bikeSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="bikeSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-26"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-26"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group"
      >
        <div
          className="bp5-form-content"
        >
          <div
            className="feature-group-header"
          >
            <h4
              className="feature-group-title-no-icon"
            >
              form.featureGroup.productsForOperators
            </h4>
          </div>
          <hr
            className="feature-group-separator"
          />
          <div
            className="feature-group"
          >
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="cityCarSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.cityCarSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="cityCarSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-27"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-27"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="estateCarSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.estateCarSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="estateCarSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-28"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-28"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="carRental"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.carRental
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="carRentalSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-29"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-29"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group"
      >
        <div
          className="bp5-form-content"
        >
          <div
            className="feature-group-header"
          >
            <h4
              className="feature-group-title-no-icon"
            >
              form.featureGroup.fleetHardware
            </h4>
          </div>
          <hr
            className="feature-group-separator"
          />
          <div
            className="feature-group"
          >
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="obdDataStick"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.obdDataStick
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="obdDataStickSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-30"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-30"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="electronicKeyCabinet"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.electronicKeyCabinet
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="electronicKeyCabinetSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-31"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-31"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="carSharingKit"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.carSharingKit
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="carSharingKitSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-32"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-32"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <style />
        </div>
      </div>
      <div>
        <div
          className="bp5-form-group"
          style={
            {
              "margin": 0,
            }
          }
        >
          <div
            className="bp5-form-content button-row"
          >
            <div
              className="center-content"
            >
              <div
                className="circle "
              >
                <style />
              </div>
              <div
                className="circle selected"
              >
                <style />
              </div>
            </div>
            <button
              className="bp5-button bp5-large bp5-intent-primary"
              disabled={false}
              onBlur={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              type="submit"
            >
              <span
                className="bp5-button-text"
              >
                form.action.send
              </span>
            </button>
          </div>
        </div>
        <style />
      </div>
    </div>
  </div>
  <div
    autoFocus={true}
  >
    <div
      className="loader-overlay rf-flex-container"
    >
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
      <div
        className="center-content"
      >
        <img
          alt="company logo icon"
          className="spinner-logo"
          src="/assets/images/spinner-logo.png"
        />
        <img
          alt="company logo text"
          src="/assets/images/spinner-text.png"
        />
      </div>
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
    </div>
    <style />
  </div>
  <style />
</form>
`;

exports[`DesiredFeaturesRight Renders without errors 1`] = `
<form
  autoComplete="off"
  onSubmit={[Function]}
>
  <div
    className="bp5-card bp5-elevation-0"
  >
    <div
      className="rf-cell"
    >
      <h3
        className="title"
      >
        Test title
      </h3>
      <h5
        className="subtitle"
      >
        Some subtitle
      </h5>
      <style />
      <div
        className="bp5-form-group"
      >
        <div
          className="bp5-form-content"
        >
          <div
            className="feature-group-header"
          >
            <h4
              className="feature-group-title-no-icon"
            >
              form.featureGroup.productsForFleets
            </h4>
          </div>
          <hr
            className="feature-group-separator"
          />
          <div
            className="feature-group"
          >
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="corporateCarSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.corporateCarSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="corporateCarSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-0"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-0"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="fleetManagement"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.fleetManagement
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="fleetManagementSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-1"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-1"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="logbook"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.logbook
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="logbookSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-2"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-2"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="driverLicenseCheck"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.driverLicenseCheck
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="driverLicenseCheckSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericPeopleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-3"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-3"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="bikeSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.bikeSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="bikeSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-4"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-4"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group"
      >
        <div
          className="bp5-form-content"
        >
          <div
            className="feature-group-header"
          >
            <h4
              className="feature-group-title-no-icon"
            >
              form.featureGroup.productsForOperators
            </h4>
          </div>
          <hr
            className="feature-group-separator"
          />
          <div
            className="feature-group"
          >
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="cityCarSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.cityCarSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="cityCarSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-5"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-5"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="estateCarSharing"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.estateCarSharing
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="estateCarSharingSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-6"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-6"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="carRental"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.carRental
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="carRentalSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-7"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-7"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <style />
        </div>
      </div>
      <div
        className="bp5-form-group"
      >
        <div
          className="bp5-form-content"
        >
          <div
            className="feature-group-header"
          >
            <h4
              className="feature-group-title-no-icon"
            >
              form.featureGroup.fleetHardware
            </h4>
          </div>
          <hr
            className="feature-group-separator"
          />
          <div
            className="feature-group"
          >
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="obdDataStick"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.obdDataStick
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="obdDataStickSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-8"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-8"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="electronicKeyCabinet"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.electronicKeyCabinet
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="electronicKeyCabinetSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-9"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-9"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="feature-checkbox-wrapper"
            >
              <label
                className="bp5-control bp5-checkbox bp5-large bp5-text-large feature-checkbox-checkbox "
              >
                <input
                  id="carSharingKit"
                  onChange={[Function]}
                  type="checkbox"
                />
                <span
                  className="bp5-control-indicator"
                />
                form.field.carSharingKit
                <style />
              </label>
              <div
                className="bp5-form-group bp5-text-large feature-checkbox-number-input hidden"
              >
                <div
                  className="bp5-form-content"
                >
                  <div
                    className="bp5-control-group bp5-fill bp5-numeric-input"
                    role="group"
                  >
                    <div
                      className="bp5-input-group"
                    >
                      <input
                        aria-valuemin={0}
                        aria-valuenow={0}
                        autoComplete="off"
                        className="bp5-input"
                        id="carSharingKitSize"
                        min={0}
                        onBlur={[Function]}
                        onChange={[Function]}
                        onCompositionEnd={[Function]}
                        onCompositionUpdate={[Function]}
                        onFocus={[Function]}
                        onKeyDown={[Function]}
                        onKeyPress={[Function]}
                        onPaste={[Function]}
                        placeholder="form.field.genericVehicleCountPlaceholder"
                        role="spinbutton"
                        style={
                          {
                            "paddingLeft": undefined,
                            "paddingRight": undefined,
                          }
                        }
                        type="text"
                        value=""
                      />
                    </div>
                    <div
                      className="bp5-button-group bp5-vertical bp5-fixed"
                    >
                      <button
                        aria-controls="numericInput-10"
                        aria-label="increment"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-up"
                        >
                          <svg
                            data-icon="chevron-up"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M254.2 134.2L174.2 214.2C170.6 217.8 165.6 220 160 220S149.4 217.8 145.8 214.2L65.8 134.2C62.2 130.6 60 125.6 60 120C60 109 69 100 80 100C85.6 100 90.6 102.2 94.2 105.8L160 171.8L225.8 106C229.4 102.2 234.4 100 240 100C251 100 260 109 260 120C260 125.6 257.8 130.6 254.2 134.2z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                      <button
                        aria-controls="numericInput-10"
                        aria-label="decrement"
                        className="bp5-button"
                        disabled={false}
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        onMouseDown={[Function]}
                        type="button"
                      >
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-chevron-down"
                        >
                          <svg
                            data-icon="chevron-down"
                            height={16}
                            role="img"
                            viewBox="0 0 16 16"
                            width={16}
                          >
                            <path
                              d="M240 220C234.4 220 229.4 217.8 225.8 214.2L160 148.2L94.2 214.2C90.6 217.8 85.6 220 80 220C69 220 60 211 60 200C60 194.4 62.2 189.4 65.8 185.8L145.8 105.8C149.4 102.2 154.4 100 160 100S170.6 102.2 174.2 105.8L254.2 185.8C257.8 189.4 260 194.4 260 200C260 211 251 220 240 220z"
                              fillRule="evenodd"
                              style={
                                {
                                  "transformOrigin": "center",
                                }
                              }
                              transform="scale(0.05, -0.05) translate(-160, -160)"
                            />
                          </svg>
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <style />
        </div>
      </div>
      <div>
        <div
          className="bp5-form-group"
          style={
            {
              "margin": 0,
            }
          }
        >
          <div
            className="bp5-form-content button-row"
          >
            <div
              className="center-content"
            >
              <div
                className="circle "
              >
                <style />
              </div>
              <div
                className="circle selected"
              >
                <style />
              </div>
            </div>
            <button
              className="bp5-button bp5-large bp5-intent-primary"
              disabled={false}
              onBlur={[Function]}
              onKeyDown={[Function]}
              onKeyUp={[Function]}
              type="submit"
            >
              <span
                className="bp5-button-text"
              >
                form.action.send
              </span>
            </button>
          </div>
        </div>
        <style />
      </div>
    </div>
  </div>
  <div
    autoFocus={true}
  >
    <div
      className="loader-overlay rf-flex-container"
    >
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
      <div
        className="center-content"
      >
        <img
          alt="company logo icon"
          className="spinner-logo"
          src="/assets/images/spinner-logo.png"
        />
        <img
          alt="company logo text"
          src="/assets/images/spinner-text.png"
        />
      </div>
      <div
        style={
          {
            "flex": 1,
          }
        }
      />
    </div>
    <style />
  </div>
  <style />
</form>
`;

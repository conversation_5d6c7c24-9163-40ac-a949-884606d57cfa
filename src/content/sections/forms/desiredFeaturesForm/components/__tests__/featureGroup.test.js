import React from 'react';

describe('NumberInput', () => {
    test('Should render correct number of features', () => {
        const {FeatureGroup} = require('../featureGroup');
        const {FeatureCheckbox} = require('../featureCheckbox');

        const component = render(<FeatureGroup
            features={['feat1', 'feat2', 'feat3']}
            icon={'some icon'}
            name={'That`s not my name'}
        />);

        const features = component.root.findAllByType(FeatureCheckbox);

        expect(features).toHaveLength(3);
        expect(component).toMatchSnapshot();
    });
});

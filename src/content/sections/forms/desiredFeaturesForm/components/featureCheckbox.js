import React from 'react';
import PropTypes from 'prop-types';
import {Checkbox, Classes} from '@blueprintjs/core';
import {global} from 'styled-jsx/css';
import _ from 'lodash';

import {T} from 'rf-i18n';
import {FormContext} from 'rf-form-utils';

import {NumberInput} from '../../additionalInformationForm/components';

const styles = global`
.feature-checkbox-wrapper {
    width: 100%;
}

.feature-checkbox-checkbox.input-visible {
    margin-bottom: 12px;
}

.feature-checkbox-number-input {
    margin-bottom: 6px;
}

.feature-checkbox-number-input.hidden {
    display: none;
}
`;

export class FeatureCheckbox extends React.Component {
    static contextType = FormContext;

    onChangeCheckbox(evt) {
        const {feature} = this.props;

        const checked = _.get(evt, 'target.checked');

        if (!checked) {
            this.context.updateField({
                path: `extended.desiredFeatures.${feature}.size`,
                evt:  {target: {value: ''}}
            });
        }

        this.context.updateField({fieldName: feature, evt});
    }

    render() {
        const {feature, placeholder} = this.props;

        const isChecked = _.get(this.context, `fields.${feature}.value`);

        return (
            <div className={'feature-checkbox-wrapper'}>
                <Checkbox
                    large
                    id={feature}
                    defaultChecked={isChecked}
                    onChange={this.onChangeCheckbox.bind(this)}
                    className={`${Classes.TEXT_LARGE} feature-checkbox-checkbox ${isChecked ? 'input-visible' : ''}`}>
                    <T>{`form.field.${feature}`}</T>
                    <style jsx global>{styles}</style>
                </Checkbox>

                <NumberInput
                    fieldName={`${feature}Size`}
                    path={`extended.desiredFeatures.${feature}.size`}
                    showLabel={false}
                    formGroupClassName={`feature-checkbox-number-input ${isChecked ? '' : 'hidden'}`}
                    placeholder={placeholder}
                />
            </div>
        );
    }
}

FeatureCheckbox.propTypes = {
    feature:     PropTypes.string,
    placeholder: PropTypes.string
};

// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SignupForm Renders without errors 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  style={
    {
      "backgroundColor": "#2B7CB8",
    }
  }
>
  <div
    className="rf-flex-container"
  >
    <div
      className="rf-cell side-container"
    >
      <h1
        className="h2 form-title"
      >
        Side image title
      </h1>
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg"
          decoding="async"
          height={3000}
          loading="lazy"
          sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, (max-width: 1922px) 1921px, 2000px"
          src="https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=2000&h=3000&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=1,0,1998,3000&w=361&h=542&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=1,0,1999,3000&w=641&h=962&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=0,1,2000,2999&w=1025&h=1537&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?rect=0,1,2000,2999&w=1281&h=1921&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=1681&h=2522&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=1921&h=2882&auto=format 1921w, https://cdn.sanity.io/images/dp11egz7/development/Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000.jpg?w=2000&h=3000&auto=format 2000w"
          style={
            {
              "height": "auto",
              "maxHeight": 3000,
              "maxWidth": 2000,
              "width": "100%",
            }
          }
          width={2000}
        />
      </div>
      <style />
    </div>
    <div
      className="rf-cell"
    >
      <form
        autoComplete="off"
        onSubmit={[Function]}
      >
        <div
          className="bp5-card bp5-elevation-0"
        >
          <div
            className="rf-cell"
          >
            <h3
              className="title"
            >
              Test title
            </h3>
            <h4
              className="subtitle"
            >
              Some subtitle
            </h4>
            <style />
            <div
              className="bp5-form-group bp5-text-large "
            >
              <label
                className="bp5-label"
                htmlFor="salutation"
              >
                form.field.salutation
                 
                <span
                  className="bp5-text-muted"
                />
              </label>
              <div
                className="bp5-form-content"
              >
                <div
                  className="bp5-radio-group"
                >
                  <label
                    className="bp5-control bp5-radio bp5-inline"
                  >
                    <input
                      checked={false}
                      name="Blueprint5.RadioGroup-0"
                      onChange={[Function]}
                      type="radio"
                      value="female"
                    />
                    <span
                      className="bp5-control-indicator"
                    />
                    form.field.salutationFemale
                  </label>
                  <label
                    className="bp5-control bp5-radio bp5-inline"
                  >
                    <input
                      checked={false}
                      name="Blueprint5.RadioGroup-0"
                      onChange={[Function]}
                      type="radio"
                      value="male"
                    />
                    <span
                      className="bp5-control-indicator"
                    />
                    form.field.salutationMale
                  </label>
                </div>
                <style />
              </div>
            </div>
            <div
              className="bp5-form-group bp5-text-large "
            >
              <label
                className="bp5-label"
                htmlFor="firstName"
              >
                form.field.firstName
                 
                <span
                  className="bp5-text-muted"
                />
              </label>
              <div
                className="bp5-form-content"
              >
                <div
                  className="bp5-input-group"
                >
                  <input
                    className="bp5-input"
                    id="firstName"
                    onChange={[Function]}
                    placeholder="form.field.firstNamePlaceholder"
                    style={
                      {
                        "paddingLeft": undefined,
                        "paddingRight": undefined,
                      }
                    }
                    type="text"
                  />
                </div>
                <style />
              </div>
            </div>
            <div
              className="bp5-form-group bp5-text-large "
            >
              <label
                className="bp5-label"
                htmlFor="lastName"
              >
                form.field.lastName
                 
                <span
                  className="bp5-text-muted"
                />
              </label>
              <div
                className="bp5-form-content"
              >
                <div
                  className="bp5-input-group"
                >
                  <input
                    className="bp5-input"
                    id="lastName"
                    onChange={[Function]}
                    placeholder="form.field.lastNamePlaceholder"
                    style={
                      {
                        "paddingLeft": undefined,
                        "paddingRight": undefined,
                      }
                    }
                    type="text"
                  />
                </div>
                <style />
              </div>
            </div>
            <div
              className="bp5-form-group bp5-text-large "
            >
              <label
                className="bp5-label"
                htmlFor="company"
              >
                form.field.company
                 
                <span
                  className="bp5-text-muted"
                />
              </label>
              <div
                className="bp5-form-content"
              >
                <div
                  className="bp5-input-group"
                >
                  <input
                    className="bp5-input"
                    id="company"
                    onChange={[Function]}
                    placeholder="form.field.companyPlaceholder"
                    style={
                      {
                        "paddingLeft": undefined,
                        "paddingRight": undefined,
                      }
                    }
                    type="text"
                  />
                </div>
                <style />
              </div>
            </div>
            <div
              className="bp5-form-group bp5-text-large "
            >
              <label
                className="bp5-label"
                htmlFor="email"
              >
                form.field.email
                 
                <span
                  className="bp5-text-muted"
                />
              </label>
              <div
                className="bp5-form-content"
              >
                <div
                  className="bp5-input-group"
                >
                  <input
                    className="bp5-input"
                    id="email"
                    onChange={[Function]}
                    placeholder="form.field.emailPlaceholder"
                    style={
                      {
                        "paddingLeft": undefined,
                        "paddingRight": undefined,
                      }
                    }
                    type="email"
                  />
                </div>
                <style />
              </div>
            </div>
            <div
              className="bp5-form-group bp5-text-large "
            >
              <label
                className="bp5-label"
                htmlFor="phone"
              >
                form.field.phone
                 
                <span
                  className="bp5-text-muted"
                />
              </label>
              <div
                className="bp5-form-content"
              >
                <div
                  className="bp5-control-group bp5-fill"
                  role="group"
                >
                  <div>
                    <div
                      aria-controls="listbox-0"
                      aria-expanded={false}
                      aria-haspopup="listbox"
                      className="bp5-popover-target"
                      onClick={[Function]}
                      onKeyDown={[Function]}
                      onKeyUp={[Function]}
                      role="combobox"
                    >
                      <button
                        aria-label="form.field.phonePrefix"
                        className="bp5-button"
                        disabled={false}
                        id="phonePrefix"
                        onBlur={[Function]}
                        onKeyDown={[Function]}
                        onKeyUp={[Function]}
                        type="button"
                      >
                        <span
                          className="bp5-button-text"
                        >
                          +1
                        </span>
                        <span
                          aria-hidden={true}
                          className="bp5-icon bp5-icon-standard bp5-icon-caret-down"
                          data-icon="caret-down"
                        />
                      </button>
                      <style />
                    </div>
                  </div>
                  <div
                    className="bp5-input-group bp5-fill"
                  >
                    <input
                      aria-label="form.field.phone"
                      className="bp5-input"
                      id="phone"
                      onChange={[Function]}
                      placeholder="2015550123"
                      style={
                        {
                          "paddingLeft": undefined,
                          "paddingRight": undefined,
                        }
                      }
                      type="tel"
                    />
                  </div>
                </div>
                <style />
              </div>
            </div>
            <div
              className="bp5-form-group"
            >
              <div
                className="bp5-form-content"
              >
                <label
                  className="bp5-control bp5-checkbox bp5-large bp5-text-large"
                >
                  <input
                    id="subscribeToNewsletter"
                    onChange={[Function]}
                    type="checkbox"
                  />
                  <span
                    className="bp5-control-indicator"
                  />
                  form.field.subscribeToNewsletter
                </label>
              </div>
            </div>
            <div>
              <div
                className="bp5-form-group"
                style={
                  {
                    "margin": 0,
                  }
                }
              >
                <div
                  className="bp5-form-content button-row"
                >
                  <div
                    className="center-content"
                  />
                  <button
                    className="bp5-button bp5-large bp5-intent-primary"
                    disabled={false}
                    onBlur={[Function]}
                    onKeyDown={[Function]}
                    onKeyUp={[Function]}
                    type="submit"
                  >
                    <span
                      className="bp5-button-text"
                    >
                      form.action.submit
                    </span>
                  </button>
                </div>
              </div>
              <style />
            </div>
            <div
              style={
                {
                  "marginTop": "15px",
                }
              }
            >
              form.privacyPolicyText
               
              <a>
                form.privacyPolicy
              </a>
              .
            </div>
          </div>
        </div>
        <div
          autoFocus={true}
        >
          <div
            className="loader-overlay rf-flex-container"
          >
            <div
              style={
                {
                  "flex": 1,
                }
              }
            />
            <div
              className="center-content"
            >
              <img
                alt="company logo icon"
                className="spinner-logo"
                src="/assets/images/spinner-logo.png"
              />
              <img
                alt="company logo text"
                src="/assets/images/spinner-text.png"
              />
            </div>
            <div
              style={
                {
                  "flex": 1,
                }
              }
            />
          </div>
          <style />
        </div>
        <style />
      </form>
    </div>
  </div>
</section>
`;

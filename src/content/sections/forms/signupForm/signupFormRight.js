import React, {Component} from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

import {navigationUtil} from 'rf-utils';
import {salesApi, FormContext, getProcessedForm, updateField, getFormErrors} from 'rf-form-utils';

import {FormFooter, FormHeader, Form, TextInput, SalutationInput, PhoneInput, NewsletterSignup, PrivacyPolicy} from '../components';

export class SignupFormRight extends Component {
    static propTypes = {
        title:             PropTypes.string,
        subtitle:          PropTypes.string,
        privacyPolicyLink: PropTypes.string,
        fleetsterFormType: PropTypes.string,
        successURL:        PropTypes.string,
        salesEndpoint:     PropTypes.string
    };

    constructor(props) {
        super(props);

        this.state = {fields: {}};
    }

    validateForm(processedForm) {
        const formErrors = getFormErrors(processedForm);

        _.forEach(formErrors, (error, fieldName) => {
            this.setState(state => _.set(state, `fields.${fieldName}.error`, error));
        });

        return formErrors;
    }

    async onSubmit(evt) {
        evt.preventDefault();

        this.setState({submitError: undefined});

        const processedForm = {...getProcessedForm(this.state), source: this.props.fleetsterFormType};
        const hasErrors = this.validateForm(processedForm);
        if (!_.isEmpty(hasErrors)) { return; }

        this.setState({submitting: true});

        try {
            const {hashedId} = await salesApi.createLead(processedForm, this.props.salesEndpoint);

            navigationUtil.navigateTo(this.props.successURL, {hashedId});
        } catch (error) {
            this.setState({submitError: _.get(error, 'message', error), submitting: false});
        }
    }

    render() {
        const {title, subtitle, privacyPolicyLink} = this.props;

        return (
            <FormContext.Provider value={{...this.state, updateField: updateField.bind(this)}}>
                <Form onSubmit={this.onSubmit.bind(this)}>
                    <FormHeader title={title} subtitle={subtitle}/>

                    <SalutationInput/>
                    <TextInput fieldName={'firstName'}/>
                    <TextInput fieldName={'lastName'}/>
                    <TextInput fieldName={'company'}/>
                    <TextInput fieldName={'email'} type={'email'}/>
                    <PhoneInput/>
                    <NewsletterSignup/>

                    <FormFooter submitButtonLabel={'form.action.submit'}/>

                    <PrivacyPolicy privacyPolicyLink={privacyPolicyLink}/>
                </Form>
            </FormContext.Provider>
        );
    }
}

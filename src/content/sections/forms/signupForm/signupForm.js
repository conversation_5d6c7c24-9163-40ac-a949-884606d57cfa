import React from 'react';
import PropTypes from 'prop-types';

import {colors} from 'rf-styles';

import {FormLeft} from '../components';

import {SignupFormRight} from './signupFormRight';

export function SignupForm({sideImageTitle, sideImage, ...restProps}) {
    return (
        <section className={'page-horizontal-padding section-vertical-padding'} style={{backgroundColor: colors.lightBlue}}>
            <div className={'rf-flex-container'}>
                <FormLeft sideImage={sideImage} sideImageTitle={sideImageTitle}/>
                <div className={'rf-cell'}>
                    <SignupFormRight {...restProps} />
                </div>
            </div>
        </section>
    );
}

SignupForm.propTypes = {
    successURL:        PropTypes.string,
    fleetsterFormType: PropTypes.string,
    title:             PropTypes.string,
    subtitle:          PropTypes.string,
    privacyPolicyLink: PropTypes.string,
    sideImageTitle:    PropTypes.string,
    sideImage:         PropTypes.object
};

import React from 'react';
import _ from 'lodash';

export const FormContext = React.createContext({
    submitting:  false,
    fields:      {},
    updateField: () => {}
});

/* eslint-disable no-invalid-this */
export function updateField({fieldName, evt, path}) {
    let finalPath = path ? `fields.${path}` : `fields.${fieldName}`;

    let value;

    if (_.get(evt, 'target.type') === 'checkbox') {
        value = _.get(evt, 'target.checked');
    } else {
        value = _.get(evt, 'target.value');
    }

    this.setState(state => _.set(state, `${finalPath}.value`, value));
    this.setState(state => _.unset(state, `${finalPath}.error`));
}
/* eslint-enable no-invalid-this */


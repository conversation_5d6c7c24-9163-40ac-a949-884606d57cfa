import _ from 'lodash';

import {queryStringUtil} from 'rf-utils';
import {language} from 'rf-data/siteConfig.json';

export function getProcessedForm(form) {
    const processedForm = _.mapValues(form.fields, 'value');

    if (_.isString(processedForm.email)) {
        processedForm.email = processedForm.email.trim();
    }

    processedForm.language = language;

    if (processedForm.phone) {
        processedForm.phone = processedForm.phone && `00${processedForm.phonePrefix || 49}${processedForm.phone}`;
        delete processedForm.phonePrefix;
    }

    processedForm.internalReferrer = queryStringUtil.getFromUrl().internalReferrer;

    return processedForm;
}

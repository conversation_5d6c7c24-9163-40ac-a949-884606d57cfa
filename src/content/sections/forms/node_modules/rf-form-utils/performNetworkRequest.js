// eslint-disable-next-line max-statements
export async function performNetworkRequest(url, inputRequestInit) {
    const requestInit = Object.assign({}, inputRequestInit);

    if (!requestInit.method) {
        requestInit.method = 'GET';
    }

    if (!requestInit.headers) {
        requestInit.headers = {};
    }

    if (!requestInit.headers['Content-type']) {
        requestInit.headers['Content-type'] = 'application/json';
    }

    if (requestInit.body) {
        requestInit.body = JSON.stringify(requestInit.body);
    }

    const response = await fetch(url, requestInit);

    if (response.status >= 400 || !response.ok) {
        throw new Error(`<PERSON>rror performing ${requestInit.method} request to ${url}: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    return responseData.data || responseData;
}


import siteConfig from 'rf-data/siteConfig.json';

import {performNetworkRequest} from 'rf-form-utils/performNetworkRequest';

function getSalesEndpoint(salesEndpoint) {
    const {language} = siteConfig;

    const salesEndpoints = siteConfig.i18n[language].salesEndpoints;
    const defaultSalesEndpoint = salesEndpoints.find(endpoint => endpoint.default);

    let endpointUrl = defaultSalesEndpoint.url;

    if (salesEndpoint) {
        const url = salesEndpoints.find(endpoint => endpoint.key === salesEndpoint)?.url;

        if (url) {
            endpointUrl = url;
        }
    }

    return endpointUrl;
}

async function createLead(form, salesEndpoint) {
    const body = await performNetworkRequest(getSalesEndpoint(salesEndpoint), {method: 'POST', body: form});

    return body;
}

async function updateLead(hashedId, form, salesEndpoint) {
    const targetUrl = `${getSalesEndpoint(salesEndpoint)}/${hashedId}`;

    const body = await performNetworkRequest(targetUrl, {method: 'PUT', body: form});

    return body;
}

export const salesApi = {
    updateLead,
    createLead
};

import React from 'react';

describe('imageTextSection', () => {
    const callToAction = {
        link:      'link',
        className: 'className',
        _key:      'key',
        title:     'title',
        route:     {}
    };

    const image = {
        _type: 'image',
        asset: {
            _ref:  'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg',
            _type: 'reference'
        },
        height: 3000,
        width:  2000
    };

    const mockProps = {
        heading:          'huehuebr',
        content:          [{_type: 'embedHTML', _key: 1}, {_type: 'figure', _key: 2}],
        image,
        imageAlt:         'huehuebr',
        sendImageToRight: false,
        _key:             'some key',
        callToAction
    };

    beforeAll(() => {
        jest.doMock('rf-components/simpleBlockContent', () => ({
            __esModule: true,
            default:    ({blocks, ...rest}) => <div {...rest}>{blocks}</div> // eslint-disable-line react/display-name, react/prop-types
        }));
    });

    test('renders without errors', () => {
        const {ImageTextSection} = require('../');

        const component = render(<ImageTextSection {...mockProps}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders without errors with no heading, content or image', () => {
        const {ImageTextSection} = require('../');

        let mockPropsCopy = {...mockProps};
        delete mockPropsCopy.heading;
        delete mockPropsCopy.content;
        delete mockPropsCopy.image;

        const component = render(<ImageTextSection {...mockPropsCopy}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders without errors with no callToAction', () => {
        const {ImageTextSection} = require('../');

        let mockPropsCopy = {...mockProps};
        delete mockPropsCopy.callToAction;

        const component = render(<ImageTextSection {...mockPropsCopy}/>);

        expect(component).toMatchSnapshot();
    });

    test('Removes .reverse-section class when image is set to be on the right', () => {
        const {ImageTextSection} = require('../');
        const mockPropsCopy = {...mockProps, sendImageToRight: true};

        const component = render(<ImageTextSection {...mockPropsCopy}/>);

        const reversedDiv = component.root.findAll(el => el.props.className?.includes('reverse-section'))[0];

        expect(reversedDiv).toBeFalsy();
    });

    test('If callToAction has no title, dont show it', () => {
        const {ImageTextSection} = require('../');
        const {CallToAction} = require('rf-components');
        let callToActionCopy = {...callToAction};
        delete callToActionCopy.title;

        const mockPropsCopy = {...mockProps, callToAction: callToActionCopy};

        const component = render(<ImageTextSection {...mockPropsCopy}/>);

        expect(component.root.findAllByType(CallToAction)).toHaveLength(0);
    });

    test('If heading is falsy, dont show it', () => {
        const {ImageTextSection} = require('../');
        let mockPropsCopy = {...mockProps};
        delete mockPropsCopy.heading;

        const component = render(<ImageTextSection {...mockPropsCopy}/>);

        expect(component.root.findAllByType('h2')).toHaveLength(0);
    });
});

import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {CallToAction, SimpleBlockContent, MainHeading} from 'rf-components';
import {screenSizes} from 'rf-styles';

const styles = css`
.text-section {
    display: flex;
    align-items: center;
}
  
@media (min-width: ${screenSizes.s}px) { .text-section {
    min-width: 50%;
}}

@media (min-width: ${screenSizes.m}px) { .text-section {
    min-width: ${7 / 12 * 100}%;
}}`;

export function Text({heading = '', content = [], index, callToAction = {}}) {
    return (
        <div className={'rf-cell text-section'}>
            <div style={{width: '100%'}}>
                {heading && <MainHeading index={index} heading={heading} classNames={'h2'}/>}
                <SimpleBlockContent blocks={content}/>
                {callToAction.title &&
                <CallToAction {...callToAction}/>
                }
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

Text.propTypes = {
    heading:          PropTypes.string,
    content:          PropTypes.array,
    image:            PropTypes.object,
    imageAlt:         PropTypes.string,
    sendImageToRight: PropTypes.bool,
    callToAction:     PropTypes.object,
    index:            PropTypes.number
};

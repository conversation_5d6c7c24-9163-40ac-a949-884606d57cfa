import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {screenSizes, vSpacing} from 'rf-styles';
import {Image} from 'rf-components';

import {Text} from './text';

const styles = css`
@media (min-width: ${screenSizes.s}px) { .reverse-section {
    flex-direction: row-reverse;
}}
@media (min-width: ${screenSizes.s}px) { .image-text-section  {
    flex-wrap: nowrap;
}}

.image-vertical {
    margin-top: ${vSpacing.s}px;
}
@media (min-width: ${screenSizes.xs}px) { .image-vertical {
    margin-top: ${vSpacing.l}px;
}}
@media (min-width: ${screenSizes.s}px) { .image-vertical  {
    display: none;
}}

.image-horizontal  {
    display: none;
}  
@media (min-width: ${screenSizes.s}px) { .image-horizontal  {
    display: block;
}}`;

function ImageTextSection({sendImageToRight, image, imageAlt, ...props}) {
    const reverseClass = `${!sendImageToRight ? 'reverse-section' : ''}`;

    return (
        <section className={'page-horizontal-padding section-vertical-padding'} id={_.kebabCase(props.heading)}>
            <div className={`rf-flex-container image-text-section ${reverseClass}`}>
                <Text {...props}/>
                <div className={'rf-cell image-horizontal'}>
                    <Image image={image} imageAlt={imageAlt} layout={'fixed'}/>
                </div>
                <div className={'rf-cell image-vertical'}>
                    <Image image={image} imageAlt={imageAlt}/>
                </div>
            </div>
            <style jsx>{styles}</style>
        </section>
    );
}

ImageTextSection.propTypes = {
    heading:          PropTypes.string,
    content:          PropTypes.array,
    image:            PropTypes.object,
    imageAlt:         PropTypes.string,
    sendImageToRight: PropTypes.bool,
    callToAction:     PropTypes.object,
    _key:             PropTypes.string,
    index:            PropTypes.number
};

export {ImageTextSection};

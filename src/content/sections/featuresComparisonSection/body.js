import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {colors} from 'rf-styles';

import {Cell} from './cell';

const styles = css`
tr {
  border-top: 1px solid ${colors.lightGray};
}
`;

export function Body({columnCount, rows}) {
    if (!rows) {
        return null;
    }

    return (
        <tbody>
            {rows.map((row, i) => (
                <tr key={`${row.key}${i}`}>
                    {_.times(columnCount, j => (<Cell
                        key={`${row.key}${i}${j}`}
                        content={_.get(row, ['cells', j], '')}
                    />))}
                </tr>
            ))}
            <style jsx>{styles}</style>
        </tbody>
    );
}

Body.propTypes = {
    columnCount: PropTypes.number,
    rows:        PropTypes.array
};

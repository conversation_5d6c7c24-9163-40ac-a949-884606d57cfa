import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {screenSizes, colors} from 'rf-styles';
import {SVGIcon} from 'rf-components';

const styles = css`
th {
    text-align: center;
    padding: 9.5px;
}
th:first-child {
    text-align: left;
    width: 64%;
}
@media (min-width: ${screenSizes.xs}px) { th:first-child {
   width: 72%;
}}  
@media (min-width: ${screenSizes.s}px) { th:first-child {
    width: 10%;
}}
  
th > div {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
}
  
.header-icon {
    width: 12px;
    height: 12px;  
    background-color: ${colors.darkGray};
}
@media (min-width: ${screenSizes.xs}px) { .header-icon {
    width: 14px;
    height: 14px;
}}
@media (min-width: ${screenSizes.m}px) { .header-icon {
    width: 16px;
    height: 16px;
}}
  
th > div > span  {
   display: none;
}
@media (min-width: ${screenSizes.s}px) { th > div > span {
    display: block;
}}  
`;

export function Header({cells, icons}) {
    const cellsContent = _.map(cells, content => {
        const icon = _.find(icons, {description: content});

        return {content, icon};
    });

    return (<thead>
        <tr className={'p'}>
            {
                _.map(cellsContent, ({content, icon}, i) => (<th key={`${content}${i}`}>
                    <div>
                        {icon ? <SVGIcon image={icon.icon} className={'header-icon'} alt={icon.iconAlt} styles={styles} /> : null}
                        <span>{content}</span>
                    </div>
                </th>))
            }
        </tr>
        <style jsx>{styles}</style>
    </thead>);
}

Header.propTypes = {
    key:   PropTypes.string,
    cells: PropTypes.array,
    icons: PropTypes.array
};

import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {FleetsterIcon} from 'rf-icon';
import {colors, screenSizes} from 'rf-styles';

const styles = css`
td {
    color: ${colors.darkGray};
    text-align: center;
    padding: 9.5px;
}
td > :global(a) {
    text-decoration: none;
}
td:first-child {
    text-align: left;
    padding-left: 0;
}
 
td :global(.rf-icon) {
    width: 18px;
    height: 18px;
}
@media (min-width: ${screenSizes.xs}px) { td :global(.rf-icon) {
  width: 22px;
  height: 22px;  
}}
@media (min-width: ${screenSizes.m}px) { td :global(.rf-icon) {
  width: 25px;
  height: 25px;
}}
`;

export function Cell({content}) {
    const cleanContent = typeof content === 'string' ? content.trim().toLowerCase() : content;
    const iconStyle = {display: 'inline-block', marginBottom: -8};
    let cellContent;

    if (!cleanContent) {
        cellContent = <FleetsterIcon icon={'negative'} iconSize={'inherit'} customStyles={{...iconStyle, color: colors.gray}}/>;
    } else if (cleanContent === 'x') {
        cellContent = <FleetsterIcon icon={'confirm'} iconSize={'inherit'} customStyles={{...iconStyle, color: colors.lighterBlue}}/>;
    } else {
        cellContent = content;
    }

    return (
        <td className={'p'}>
            {cellContent}
            <style jsx>{styles}</style>
        </td>
    );
}

Cell.propTypes = {
    content: PropTypes.oneOfType([PropTypes.string, PropTypes.element])
};

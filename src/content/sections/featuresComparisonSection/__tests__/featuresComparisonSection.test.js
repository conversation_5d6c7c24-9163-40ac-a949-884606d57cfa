import React from 'react';

import {FeaturesComparisonSection} from '../';
import {featuresTableData, featuresTableIcons} from './mockData';

describe('FeaturesComparisonSection', () => {
    const mockProps = {
        heading:       'huehuebr',
        featuresTable: featuresTableData,
        icons:         featuresTableIcons
    };

    test('renders without errors', () => {
        const component = render(<FeaturesComparisonSection {...mockProps}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    test('renders without table if featuresTable is undefined', () => {
        let mockPropsCopy = {...mockProps};
        delete mockPropsCopy.featuresTable;

        const component = render(<FeaturesComparisonSection {...mockPropsCopy}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});

import React from 'react';

import {Table} from '../table';
import {featuresTableData, featuresTableIcons} from './mockData';

describe('Feature comparison table', () => {
    test('renders without errors', () => {
        const component = render(<Table data={featuresTableData} headerIcons={featuresTableIcons}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders nothing if featuresTable is undefined', () => {
        const component = render(<Table/>);

        expect(component.toJSON()).toBeNull();
    });

    test('renders links correctly when URL is provided', () => {
        let featuresTableDataWithUrl = _.cloneDeep(featuresTableData);
        featuresTableDataWithUrl.rows[0].cells.push('url');
        featuresTableDataWithUrl.rows[1].cells.push('https://www.example.com');
        featuresTableDataWithUrl.rows[2].cells.push('');

        const component = render(<Table data={featuresTableDataWithUrl} />);
        const links = component.root.findAllByType('a');
        expect(links.length).toBe(1);
        expect(links[0].props.href).toBe('https://www.example.com');

        const lastRowSecondCellIcon = component.root.findAllByType('tr')[2].findAllByType('td')[1].children[0];
        expect(lastRowSecondCellIcon.props.icon).toBe('negative');
    });
});

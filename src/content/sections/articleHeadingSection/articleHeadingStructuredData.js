import {getImageSource} from 'rf-components/image/helpers';

import {defaultManufacturer} from '../structuredDataSection/defaults';

export function articleToStructuredData(article) {
    const {image, heading, publishedOn, author, description, _type, includeInNews} = article;

    const isNewsArticle = _type === 'articleHeadingSection';

    const type = isNewsArticle && includeInNews ? 'NewsArticle' : 'Article';

    return {
        '@context':    'https://schema.org',
        '@type':       type,
        image:         getImageSource({image}),
        headline:      heading,
        datePublished: publishedOn,
        articleBody:   description,
        publisher:     defaultManufacturer,
        author:        {
            '@type': 'Person',
            name:    author
        }
    };
}

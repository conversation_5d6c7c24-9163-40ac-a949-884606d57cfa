// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ArticleHeadingSection renders without author 1`] = `
[
  <div
    className="image-holder"
  >
    <div
      className="image-inner-holder"
      style={
        {
          "minHeight": 563,
          "minWidth": 1832,
          "paddingBottom": "30.731441048034934%",
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
          decoding="async"
          loading="lazy"
          sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
          src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="page-horizontal-padding heading"
  >
    <h2
      className="h2 title"
    >
      Test Heading
    </h2>
    <p
      className="published"
    >
      20. Feb. 2020
    </p>
    <p
      className="description"
    >
       
      Test Description
       
    </p>
  </div>,
  <style />,
  <style />,
]
`;

exports[`ArticleHeadingSection renders without author and publishedOn 1`] = `
[
  <div
    className="image-holder"
  >
    <div
      className="image-inner-holder"
      style={
        {
          "minHeight": 563,
          "minWidth": 1832,
          "paddingBottom": "30.731441048034934%",
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
          decoding="async"
          loading="lazy"
          sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
          src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="page-horizontal-padding heading"
  >
    <h2
      className="h2 title"
    >
      Test Heading
    </h2>
    <p />
    <p
      className="description"
    >
       
      Test Description
       
    </p>
  </div>,
  <style />,
  <style />,
]
`;

exports[`ArticleHeadingSection renders without errors 1`] = `
[
  <div
    className="image-holder"
  >
    <div
      className="image-inner-holder"
      style={
        {
          "minHeight": 563,
          "minWidth": 1832,
          "paddingBottom": "30.731441048034934%",
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
          decoding="async"
          loading="lazy"
          sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
          src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="page-horizontal-padding heading"
  >
    <h2
      className="h2 title"
    >
      Test Heading
    </h2>
    <p
      className="published"
    >
      20. Feb. 2020 | article.by John Doe
    </p>
    <p
      className="description"
    >
       
      Test Description
       
    </p>
  </div>,
  <style />,
  <style />,
]
`;

exports[`ArticleHeadingSection renders without publishedOn 1`] = `
[
  <div
    className="image-holder"
  >
    <div
      className="image-inner-holder"
      style={
        {
          "minHeight": 563,
          "minWidth": 1832,
          "paddingBottom": "30.731441048034934%",
        }
      }
    >
      <div
        className="center-content"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
      >
        <img
          alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
          decoding="async"
          loading="lazy"
          sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
          src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
          srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        />
      </div>
    </div>
  </div>,
  <div
    className="page-horizontal-padding heading"
  >
    <h2
      className="h2 title"
    >
      Test Heading
    </h2>
    <p
      className="published"
    >
      article.by John Doe
    </p>
    <p
      className="description"
    >
       
      Test Description
       
    </p>
  </div>,
  <style />,
  <style />,
]
`;

import React from 'react';

describe('ArticleHeadingSection', () => {
    const image = {
        _type: 'image',
        asset: {
            _ref:  'image-dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563-png',
            _type: 'reference'
        },
        height: 563,
        width:  1832
    };
    test('renders without errors', () => {
        const {ArticleHeadingSection} = require('../articleHeadingSection');

        const mockProps = {
            image,
            heading:     'Test Heading',
            publishedOn: '2020-02-20',
            author:      '<PERSON>',
            description: 'Test Description'
        };

        const component = render(<ArticleHeadingSection {...mockProps}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders without publishedOn', () => {
        const {ArticleHeadingSection} = require('../articleHeadingSection');

        const mockProps = {
            image,
            heading:     'Test Heading',
            author:      '<PERSON>',
            description: 'Test Description'
        };

        const component = render(<ArticleHeadingSection {...mockProps}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders without author', () => {
        const {ArticleHeadingSection} = require('../articleHeadingSection');

        const mockProps = {
            image,
            heading:     'Test Heading',
            publishedOn: '2020-02-20',
            description: 'Test Description'
        };

        const component = render(<ArticleHeadingSection {...mockProps}/>);

        expect(component).toMatchSnapshot();
    });

    test('renders without author and publishedOn', () => {
        const {ArticleHeadingSection} = require('../articleHeadingSection');

        const mockProps = {
            image,
            heading:     'Test Heading',
            description: 'Test Description'
        };

        const component = render(<ArticleHeadingSection {...mockProps}/>);

        expect(component).toMatchSnapshot();
    });
});

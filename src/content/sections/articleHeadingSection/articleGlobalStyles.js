import {global} from 'styled-jsx/css';

import {screenSizes, vSpacing} from 'rf-styles';

const videoAspectRatio = 100 / 16 * 9;

export const articleGlobalStyles = global`
.article .page-horizontal-padding {
    box-sizing: border-box;
    padding-left: 35px;
    padding-right: 35px;
}

@media (min-width: 1500px) {
    .article .text-section .call-to-action {
        width: 100%;
    }
}

@media (min-width: ${screenSizes.xs}px) { .article .page-horizontal-padding {
    padding-left: 58px;
    padding-right: 58px;
}}
@media (min-width: ${screenSizes.s}px) { .article .page-horizontal-padding {
    padding-left: 182px;
    padding-right: 182px;
}}
@media (min-width: ${screenSizes.m}px) { .article .page-horizontal-padding {
    padding-left: 298px;
    padding-right: 298px;
}}
@media (min-width: ${screenSizes.l}px) { .article .page-horizontal-padding {
    padding-left: 405px;
    padding-right: 405px;
}}
@media (min-width: ${screenSizes.xl}px) { .article .page-horizontal-padding {
    padding-left: 433px;
    padding-right: 433px;
}}
@media (min-width: ${screenSizes.xxl}px) { .article .page-horizontal-padding {
    padding-left: 614px;
    padding-right: 614px;
}}

.article .section-vertical-padding {
    padding-bottom: 0;
    padding-top: 0;
}

@media (min-width: ${screenSizes.xxs}px) { .article section:first-of-type {
    margin-top: ${vSpacing.s}px;
}}
@media (min-width: ${screenSizes.m}px) { .article section:first-of-type {
    margin-top: ${vSpacing.m}px;
}}

.article section {
    margin-bottom: ${vSpacing.m}px;
}

.article section .text-section .call-to-action {
    margin-top: ${vSpacing.m}px;
}

.article section div:last-of-type p:last-child {
    margin-bottom: 0;
}

.article section .image-text-section {
    flex-direction: column;
    flex-wrap: wrap;
}
.article section .image-horizontal {
    display: none;
}
.article section .image-vertical {
    display: block;
}

.article section .text-section {
    width: 100%;
}
.article section .video {
    margin: ${vSpacing.l}px 0 0;
    width: 100%;
    padding-bottom: ${videoAspectRatio}%;
}
.article section .hero-text {
    margin-bottom: ${vSpacing.l}px;
}

.article .rf-flex-container {
    padding: 0;
    flex-direction: column;
}

.article .rf-cell {
    padding: 0;
}

.article section.hero-background .rf-flex-container {
    padding-top: ${vSpacing.m}px;
    padding-bottom: ${vSpacing.m}px;
}
`;

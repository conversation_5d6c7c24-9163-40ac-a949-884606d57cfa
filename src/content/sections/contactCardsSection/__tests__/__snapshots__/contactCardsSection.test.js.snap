// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ContactCardsSection renders with a single card 1`] = `
<section
  className="contact-cards-section"
>
  <div
    className="center-content contact-card"
  >
    <div
      className="contact-card-body"
    >
      <div
        className="banner-container"
      >
        <div
          className="contact-card-bg"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Contact Background"
              decoding="async"
              height={563}
              loading="lazy"
              sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
              style={
                {
                  "height": "100%",
                  "objectFit": "cover",
                  "width": "100%",
                }
              }
              width={1832}
            />
          </div>
          <style />
        </div>
        <div
          className="avatar-container"
        >
          <div
            className="avatar-container"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
              decoding="async"
              height={160}
              loading="lazy"
              sizes="160px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format 160w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 160,
                  "maxWidth": 160,
                  "width": "100%",
                }
              }
              width={160}
            />
          </div>
        </div>
      </div>
      <style />
      <div
        className="contact-card-title"
      >
        <h3
          className="h3"
        >
          Name
        </h3>
        <h4
          className="h6"
        >
          Function
        </h4>
        <style />
      </div>
      <ul>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              ItemONe
            </span>
          </div>
        </li>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              IOtem Two
            </span>
          </div>
        </li>
        <style />
      </ul>
      <div
        className="rf-cell"
      />
      <div
        className="contact-card-buttons"
      >
        <div
          className="contact-card-button light-theme"
        >
          <div
            className="item-icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="undefined icon"
                decoding="async"
                height={563}
                loading="lazy"
                sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 563,
                    "maxWidth": 1832,
                    "width": "100%",
                  }
                }
                width={1832}
              />
            </div>
          </div>
          <a
            className="center-content light-theme "
            rel="noreferrer"
            target="_blank"
          >
            CLickme
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</section>
`;

exports[`ContactCardsSection renders with multiple cards 1`] = `
<section
  className="contact-cards-section"
>
  <div
    className="center-content contact-card"
  >
    <div
      className="contact-card-body"
    >
      <div
        className="banner-container"
      >
        <div
          className="contact-card-bg"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Contact Background"
              decoding="async"
              height={563}
              loading="lazy"
              sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
              style={
                {
                  "height": "100%",
                  "objectFit": "cover",
                  "width": "100%",
                }
              }
              width={1832}
            />
          </div>
          <style />
        </div>
        <div
          className="avatar-container"
        >
          <div
            className="avatar-container"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
              decoding="async"
              height={160}
              loading="lazy"
              sizes="160px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format 160w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 160,
                  "maxWidth": 160,
                  "width": "100%",
                }
              }
              width={160}
            />
          </div>
        </div>
      </div>
      <style />
      <div
        className="contact-card-title"
      >
        <h3
          className="h3"
        >
          Name
        </h3>
        <h4
          className="h6"
        >
          Function
        </h4>
        <style />
      </div>
      <ul>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              ItemONe
            </span>
          </div>
        </li>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              IOtem Two
            </span>
          </div>
        </li>
        <style />
      </ul>
      <div
        className="rf-cell"
      />
      <div
        className="contact-card-buttons"
      >
        <div
          className="contact-card-button light-theme"
        >
          <div
            className="item-icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="undefined icon"
                decoding="async"
                height={563}
                loading="lazy"
                sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 563,
                    "maxWidth": 1832,
                    "width": "100%",
                  }
                }
                width={1832}
              />
            </div>
          </div>
          <a
            className="center-content light-theme "
            rel="noreferrer"
            target="_blank"
          >
            CLickme
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <div
    className="center-content contact-card"
  >
    <div
      className="contact-card-body"
    >
      <div
        className="banner-container"
      >
        <div
          className="contact-card-bg"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Contact Background"
              decoding="async"
              height={563}
              loading="lazy"
              sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
              style={
                {
                  "height": "100%",
                  "objectFit": "cover",
                  "width": "100%",
                }
              }
              width={1832}
            />
          </div>
          <style />
        </div>
        <div
          className="avatar-container"
        >
          <div
            className="avatar-container"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
              decoding="async"
              height={160}
              loading="lazy"
              sizes="160px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format 160w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 160,
                  "maxWidth": 160,
                  "width": "100%",
                }
              }
              width={160}
            />
          </div>
        </div>
      </div>
      <style />
      <div
        className="contact-card-title"
      >
        <h3
          className="h3"
        >
          Name,  
        </h3>
        <h4
          className="h6"
        >
          Some Role
        </h4>
        <style />
      </div>
      <ul>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <span
              className="item-description"
            >
              No image
            </span>
          </div>
        </li>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            />
          </div>
        </li>
        <style />
      </ul>
      <div
        className="rf-cell"
      />
      <div
        className="contact-card-buttons"
      >
        <div
          className="contact-card-button light-theme"
        >
          <div
            className="item-icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="undefined icon"
                decoding="async"
                height={563}
                loading="lazy"
                sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 563,
                    "maxWidth": 1832,
                    "width": "100%",
                  }
                }
                width={1832}
              />
            </div>
          </div>
          <a
            className="center-content light-theme "
            rel="noreferrer"
            target="_blank"
          >
            Button One
            <style />
          </a>
          <style />
        </div>
        <div
          className="contact-card-button "
        >
          <div
            className="item-icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="undefined icon"
                decoding="async"
                height={563}
                loading="lazy"
                sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 563,
                    "maxWidth": 1832,
                    "width": "100%",
                  }
                }
                width={1832}
              />
            </div>
          </div>
          <a
            className="center-content  "
            rel="noreferrer"
            target="_blank"
          >
            Button Two
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</section>
`;

exports[`ContactCardsSection renders without background images 1`] = `
<section
  className="contact-cards-section"
>
  <div
    className="center-content contact-card"
  >
    <div
      className="contact-card-body"
    >
      <div
        className="banner-container"
      >
        <div
          className="avatar-container"
        >
          <div
            className="avatar-container"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
              decoding="async"
              height={160}
              loading="lazy"
              sizes="160px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format 160w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 160,
                  "maxWidth": 160,
                  "width": "100%",
                }
              }
              width={160}
            />
          </div>
        </div>
      </div>
      <style />
      <div
        className="contact-card-title"
      >
        <h3
          className="h3"
        >
          Name
        </h3>
        <h4
          className="h6"
        >
          Function
        </h4>
        <style />
      </div>
      <ul>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              ItemONe
            </span>
          </div>
        </li>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              IOtem Two
            </span>
          </div>
        </li>
        <style />
      </ul>
      <div
        className="rf-cell"
      />
      <div
        className="contact-card-buttons"
      >
        <div
          className="contact-card-button light-theme"
        >
          <div
            className="item-icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="undefined icon"
                decoding="async"
                height={563}
                loading="lazy"
                sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 563,
                    "maxWidth": 1832,
                    "width": "100%",
                  }
                }
                width={1832}
              />
            </div>
          </div>
          <a
            className="center-content light-theme "
            rel="noreferrer"
            target="_blank"
          >
            CLickme
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</section>
`;

exports[`ContactCardsSection renders without buttons 1`] = `
<section
  className="contact-cards-section"
>
  <div
    className="center-content contact-card"
  >
    <div
      className="contact-card-body"
    >
      <div
        className="banner-container"
      >
        <div
          className="contact-card-bg"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Contact Background"
              decoding="async"
              height={563}
              loading="lazy"
              sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
              style={
                {
                  "height": "100%",
                  "objectFit": "cover",
                  "width": "100%",
                }
              }
              width={1832}
            />
          </div>
          <style />
        </div>
        <div
          className="avatar-container"
        >
          <div
            className="avatar-container"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
              decoding="async"
              height={160}
              loading="lazy"
              sizes="160px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format 160w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 160,
                  "maxWidth": 160,
                  "width": "100%",
                }
              }
              width={160}
            />
          </div>
        </div>
      </div>
      <style />
      <div
        className="contact-card-title"
      >
        <h3
          className="h3"
        >
          Name
        </h3>
        <h4
          className="h6"
        >
          Function
        </h4>
        <style />
      </div>
      <ul>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              ItemONe
            </span>
          </div>
        </li>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              IOtem Two
            </span>
          </div>
        </li>
        <style />
      </ul>
      <div
        className="rf-cell"
      />
    </div>
    <style />
  </div>
  <style />
</section>
`;

exports[`ContactCardsSection renders without errors 1`] = `
<section
  className="contact-cards-section"
>
  <style />
</section>
`;

exports[`ContactCardsSection renders without name and role 1`] = `
<section
  className="contact-cards-section"
>
  <div
    className="center-content contact-card"
  >
    <div
      className="contact-card-body"
    >
      <div
        className="banner-container"
      >
        <div
          className="contact-card-bg"
        >
          <div
            className="center-content"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="Contact Background"
              decoding="async"
              height={563}
              loading="lazy"
              sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
              style={
                {
                  "height": "100%",
                  "objectFit": "cover",
                  "width": "100%",
                }
              }
              width={1832}
            />
          </div>
          <style />
        </div>
        <div
          className="avatar-container"
        >
          <div
            className="avatar-container"
            style={
              {
                "height": "100%",
                "width": "100%",
              }
            }
          >
            <img
              alt="/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png"
              decoding="async"
              height={160}
              loading="lazy"
              sizes="160px"
              src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format"
              srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=635,0,563,563&w=160&h=160&auto=format 160w"
              style={
                {
                  "height": "auto",
                  "maxHeight": 160,
                  "maxWidth": 160,
                  "width": "100%",
                }
              }
              width={160}
            />
          </div>
        </div>
      </div>
      <style />
      <ul>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              ItemONe
            </span>
          </div>
        </li>
        <li
          className="list-item"
        >
          <div
            className="list-item-container"
          >
            <div
              className="item-icon"
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="undefined icon"
                  decoding="async"
                  height={563}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 563,
                      "maxWidth": 1832,
                      "width": "100%",
                    }
                  }
                  width={1832}
                />
              </div>
            </div>
            <span
              className="item-description"
            >
              IOtem Two
            </span>
          </div>
        </li>
        <style />
      </ul>
      <div
        className="rf-cell"
      />
      <div
        className="contact-card-buttons"
      >
        <div
          className="contact-card-button light-theme"
        >
          <div
            className="item-icon"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="undefined icon"
                decoding="async"
                height={563}
                loading="lazy"
                sizes="(max-width: 362px) 361px, (max-width: 642px) 641px, (max-width: 1026px) 1025px, (max-width: 1282px) 1281px, (max-width: 1682px) 1681px, 1832px"
                src="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=361&h=111&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=641&h=197&auto=format 641w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1025&h=315&auto=format 1025w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1830,563&w=1281&h=394&auto=format 1281w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?rect=1,0,1831,563&w=1681&h=517&auto=format 1681w, https://cdn.sanity.io/images/dp11egz7/development/dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563.png?w=1832&h=563&auto=format 1832w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 563,
                    "maxWidth": 1832,
                    "width": "100%",
                  }
                }
                width={1832}
              />
            </div>
          </div>
          <a
            className="center-content light-theme "
            rel="noreferrer"
            target="_blank"
          >
            CLickme
            <style />
          </a>
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
</section>
`;

import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {screenSizes, hSpacing} from 'rf-styles';

import {ContactCardTitle} from './contactCardTitle';
import {ContactCardButtons} from './contactCardButtons';
import {ContactCardBanner} from './contactCardBanner';
import {ContactCardListItems} from './contactCardListItems';

const styles = css`
  .contact-card {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex-basis: 100%;
    box-sizing: unset;
    align-self: stretch;
  }

  @media (min-width: ${screenSizes.xs}px) {
    .contact-card {
      max-width: calc(50% - ${hSpacing.s}px);
    }
  }

  @media (min-width: ${screenSizes.s}px) {
    .contact-card {
      max-width: calc(33.3% - ${hSpacing.sm}px);
    }
  }

  .contact-card-body {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-bottom: ${hSpacing.sm}px;
    border: 1px solid #CCCCCC;
    border-radius: 10px;
    background-size: cover;
    background-position-x: right;
    box-shadow: 0 1px 0 1px rgba(0, 0, 0, 0.05);
    flex-grow: 1;
  }
`;

function ContactCard({card}) {
    const {name, role, image, backgroundImage, itemList, buttons} = card;

    return (
        <div className={'center-content contact-card'}>
            <div className={'contact-card-body'}>
                <ContactCardBanner image={image} backgroundImage={backgroundImage}/>
                <ContactCardTitle name={name} role={role}/>
                <ContactCardListItems items={itemList}/>
                <div className={'rf-cell'}/>
                <ContactCardButtons buttons={buttons}/>
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

ContactCard.propTypes = {
    _key: PropTypes.string,
    card: PropTypes.object
};

export {ContactCard};

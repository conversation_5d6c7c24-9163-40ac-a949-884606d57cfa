import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {Image} from 'rf-components';
import {screenSizes} from 'rf-styles';

import {CONTACT_CARD_AVATAR_SIZE, CONTACT_CARD_BANNER_HEIGHT} from './constants';
import {ContactCardBackgroundImage} from './contactCardBackgroundImage';

const styles = css`
  .banner-container {
    text-align: center;
    max-height: ${CONTACT_CARD_BANNER_HEIGHT}px;
    margin-bottom: 40px;
    display: none;
    border-radius: 10px 10px 0 0;
    position: relative;
  }

  @media (min-width: ${screenSizes.xs}px) {
    .banner-container {
      display: block;
    }
  }
  
  .avatar-container {
    position: inherit;
  }
  
  .avatar-container :global(img) {
    width:        100%;
    height:       auto;
    max-width:     ${CONTACT_CARD_AVATAR_SIZE}px;
    max-height:    ${CONTACT_CARD_AVATAR_SIZE}px;
    border:        5px solid white;
    border-radius: 100%;
    margin-top:    20px;
  }
`;

function ContactCardBanner({image, backgroundImage}) {
    return (
        <>
            <div className={'banner-container'}>
                <ContactCardBackgroundImage image={backgroundImage}/>
                <div className={'avatar-container'}>
                    <Image
                        className={'avatar-container'}
                        width={CONTACT_CARD_AVATAR_SIZE}
                        height={CONTACT_CARD_AVATAR_SIZE}
                        image={image}/>
                </div>
            </div>
            <style jsx>{styles}</style>
        </>
    );
}

ContactCardBanner.propTypes = {
    image: PropTypes.object
};

export {ContactCardBanner};

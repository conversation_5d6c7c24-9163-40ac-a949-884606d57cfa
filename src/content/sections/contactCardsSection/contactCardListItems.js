import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';
import * as _ from 'lodash';

import {Image} from 'rf-components';
import {colors, fontWeights, hSpacing, screenSizes, vSpacing} from 'rf-styles';

const styles = css`
  ul {
    padding: 0 10px;
    margin: 0;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-flow: wrap;
  }
  
  .list-item {
    display: inline-block;
    text-decoration: none;
    padding: 0 ${vSpacing.xs}px;
  }

  .list-item-container {
    background-color: ${colors.contactCardIcon};
    height: 30px;
    display: flex;
    justify-content: center;
    padding: ${vSpacing.xs}px ${hSpacing.xs}px;
    font-size: 12px;
    line-height: 16px;
    align-items: center;
    border: 1px solid white;
    border-radius: 5px;
  }

  .item-icon {
    max-width: 20px;
    min-width: 20px;
    flex: 0;
    color: ${colors.gray};
    fill: ${colors.contactCardIcon};
    margin-right: 5px;
  }

  .item-description {
    color: ${colors.darkGray};
    font-weight: ${fontWeights.semiBold};
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .list-item {
      margin-top: ${hSpacing.xxs}px
    }
  }
`;

function ContactCardListItems({items}) {
    return (
        <ul>
            {_.map(items, (item, i) => {
                const {icon, description, iconAlt} = item;

                return (<li className={'list-item'} key={icon + i}>
                    <div className={'list-item-container'}>
                        {icon && <div className={'item-icon'}>
                            <Image image={icon} imageAlt={`${iconAlt} icon`}/>
                        </div>}
                        <span className={'item-description'}>{description}</span>
                    </div>
                </li>);
            })}
            <style jsx>{styles}</style>
        </ul>
    );
}

ContactCardListItems.propTypes = {
    items: PropTypes.array
};

export {ContactCardListItems};

import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {screenSizes, hSpacing, vSpacing} from 'rf-styles';

const styles = css`
  .h3 {
    margin-top: ${hSpacing.xs}px;
    margin-bottom: ${vSpacing.xs}px;
    text-align: center;
  }

  .h6 {
    text-align: center;
    margin-top: 0;
    margin-bottom: 10px;
  }

  .contact-card-title {
    width: 100%;
  }

  @media (min-width: ${screenSizes.xxs}px) {
    .listItem {
      width: 100%;
      margin-top: ${hSpacing.xs}px
    }
  }`;

function ContactCardTitle({name, role}) {
    if (!name || !role) {
        return null;
    }

    return (
        <div className={'contact-card-title'}>
            <h3 className={'h3'}>{name}</h3>
            <h6 className={'h6'}>{role}</h6>
            <style jsx>{styles}</style>
        </div>
    );
}

ContactCardTitle.propTypes = {
    _key: PropTypes.string,
    name: PropTypes.string,
    role: PropTypes.string
};

export {ContactCardTitle};

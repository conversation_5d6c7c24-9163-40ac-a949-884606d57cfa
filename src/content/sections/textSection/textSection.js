import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

import {TextSectionRight} from './textSectionRight';
import {TextSectionLeft} from './textSectionLeft';

export function TextSection({leftColumn = {}, rightColumn = {}, index}) {
    return (
        <section className={'page-horizontal-padding section-vertical-padding'} id={_.kebabCase(leftColumn.heading)}>
            <div className={'rf-flex-container'}>
                <TextSectionLeft {...leftColumn} index={index}/>
                <TextSectionRight {...rightColumn}/>
            </div>
        </section>
    );
}

TextSection.propTypes = {
    leftColumn:  PropTypes.object,
    rightColumn: PropTypes.object,
    index:       PropTypes.number
};

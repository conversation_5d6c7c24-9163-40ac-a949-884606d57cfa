import React from 'react';
import {TextSection} from '../';

jest.mock('rf-components/simpleBlockContent', () => ({
    __esModule: true,
    default:    ({blocks, ...rest}) => <div {...rest}>{blocks}</div> // eslint-disable-line react/display-name, react/prop-types
}));

describe('TextSection', () => {
    const mockProps = {
        leftColumn: {
            content:    [{_type: 'embedHTML', _key: 1}, {_type: 'figure', _key: 2}],
            footnote:   '<a href="#" target="_blank">Footnote Left</a>',
            heading:    'Heading Left',
            makeBold:   false,
            subheading: 'Subheading Left'
        },
        rightColumn: {
            content:    'Content Right <script>alert("Evil Script")</script>',
            footnote:   '<em>Footnote</em> Right <img src="http://notagoodwebsite.com/evil-image.png"/>',
            heading:    'Heading Right',
            makeBold:   true,
            subheading: 'Subheading Right'
        }
    };

    test('Renders with no data', () => {
        const component = render(<TextSection/>);

        expect(component).toMatchSnapshot();
    });

    test('Does not render right text column if it has no content', () => {
        const mockPropsCopy = {...mockProps, rightColumn: {}};

        const component = render(<TextSection {...mockPropsCopy}/>);

        expect(component.root.findAllByType('h3')).toHaveLength(0);
    });

    test('Does not render left text column if it has no content', () => {
        const mockPropsCopy = {...mockProps, leftColumn: {}};

        const component = render(<TextSection {...mockPropsCopy}/>);

        expect(component.root.findAllByType('p')).toHaveLength(1);
    });

    test('does not render empty props', () => {
        mockProps.rightColumn.subheading = '';

        const component = render(<TextSection {...mockProps}/>);

        expect(component).toMatchSnapshot();
    });
});

import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, fontWeights} from 'rf-styles';

const styles = css`
.heading-dash {
    color: ${colors.lightBlue};
    font-weight: ${fontWeights.bold};
    font-size: 60px;
    line-height:60px;
}

.footnote {
    font-weight: ${fontWeights.semiBold};
    color: ${colors.lightBlue};    
    margin-bottom: 0;
}`;

export function TextSectionRight({content, footnote}) {
    if (!content) {
        return null;
    }

    return (
        <>
            <div className={'rf-flex-1'}/>
            <div className={'rf-cell rf-flex-5'}>
                <div className={'h5 heading-dash'}>_</div>
                <h3 className={'h3'} style={{fontWeight: fontWeights.semiBold}} dangerouslySetInnerHTML={{__html: content}}/>
                {footnote && <p className={'footnote'} dangerouslySetInnerHTML={{__html: footnote}}/>}
                <style jsx>{styles}</style>
            </div>
        </>
    );
}

TextSectionRight.propTypes = {
    content:  PropTypes.string,
    footnote: PropTypes.string
};

import React from 'react';

import {ProductStructuredData} from '../productStructuredData';

jest.mock('next/router', () => ({
    useRouter: jest.fn().mockReturnValue({asPath: '/some/path'})
}));

// eslint-disable-next-line react/prop-types
jest.mock('next/head', () => ({children}) => <div>{children}</div>);

describe('ProductStructuredData', () => {
    it('does not render if data is not passed', () => {
        const component = render(<ProductStructuredData/>);

        expect(component.toJSON()).toBeNull();
    });

    it('renders the parsed data in a script tag', () => {
        const data = {
            brand:        {foo: 'bar'},
            manufacturer: {foo: 'bar', location: {foo: 'bar'}}
        };
        const component = render(<ProductStructuredData {...data}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });

    it('adds the default brand and manufacturer if not set', () => {
        const data = {name: 'showDefaults'};
        const component = render(<ProductStructuredData {...data}/>);

        const generatedStructuredData = component.toJSON()
            .children[0]
            .props
            .dangerouslySetInnerHTML
            .__html;

        expect(Object.keys(JSON.parse(generatedStructuredData))).toContain('brand');
        expect(Object.keys(JSON.parse(generatedStructuredData))).toContain('manufacturer');
    });

    it('transforms a single offer correctly', () => {
        const data = {
            name:   'Product with single offer',
            offers: {
                offers: [
                    {
                        price:         '19.99',
                        priceCurrency: 'USD'
                    }
                ]
            }
        };
        const component = render(<ProductStructuredData {...data}/>);

        const generatedStructuredData = JSON.parse(component.toJSON()
            .children[0]
            .props
            .dangerouslySetInnerHTML
            .__html);

        expect(generatedStructuredData.offers['@type']).toBe('Offer');
        expect(generatedStructuredData.offers.price).toBe('19.99');
        expect(generatedStructuredData.offers.priceCurrency).toBe('USD');
        expect(generatedStructuredData.offers.offers).toBeUndefined();
    });

    it('sets aggregate offer fields for multiple offers', () => {
        const data = {
            name:   'Product with multiple offers',
            offers: {
                offers: [
                    {
                        price:         '19.99',
                        priceCurrency: 'USD'
                    },
                    {
                        price:         '17.50',
                        priceCurrency: 'EUR'
                    }
                ]
            }
        };
        const component = render(<ProductStructuredData {...data}/>);

        const generatedStructuredData = JSON.parse(component.toJSON()
            .children[0]
            .props
            .dangerouslySetInnerHTML
            .__html);

        expect(generatedStructuredData.offers['@type']).toBe('AggregateOffer');
        expect(generatedStructuredData.offers.lowPrice).toBe('17.5');
        expect(generatedStructuredData.offers.priceCurrency).toBe('EUR');
        expect(generatedStructuredData.offers.offerCount).toBe(2);
        expect(generatedStructuredData.offers.offers.length).toBe(2);
    });

    it('handles decimal prices correctly in aggregate offers', () => {
        const data = {
            name:   'Product with decimal prices',
            offers: {
                offers: [
                    {
                        price:         '10.50',
                        priceCurrency: 'USD'
                    },
                    {
                        price:         '9.75',
                        priceCurrency: 'EUR'
                    },
                    {
                        price:         '11.25',
                        priceCurrency: 'GBP'
                    }
                ]
            }
        };
        const component = render(<ProductStructuredData {...data}/>);

        const generatedStructuredData = JSON.parse(component.toJSON()
            .children[0]
            .props
            .dangerouslySetInnerHTML
            .__html);

        expect(generatedStructuredData.offers.lowPrice).toBe('9.75');
        expect(generatedStructuredData.offers.priceCurrency).toBe('EUR');
        expect(generatedStructuredData.offers.offerCount).toBe(3);
    });

    it('gracefully handles missing price values', () => {
        const data = {
            name:   'Product with missing prices',
            offers: {
                '@type': 'AggregateOffer',
                offers:  [
                    {
                        priceCurrency: 'USD'
                    },
                    {
                        price:         '25',
                        priceCurrency: 'EUR'
                    }
                ]
            }
        };
        const component = render(<ProductStructuredData {...data}/>);

        const generatedStructuredData = JSON.parse(component.toJSON()
            .children[0]
            .props
            .dangerouslySetInnerHTML
            .__html);

        expect(generatedStructuredData.offers.lowPrice).toBe('0');
        expect(generatedStructuredData.offers.priceCurrency).toBe('USD');
    });
});

import _ from 'lodash';

import {languages} from 'rf-data/siteConfig.json';

export const typeDictionary = {
    manufacturer: 'Organization',
    location:     'PostalAddress',
    offers:       'AggregateOffer'
};

export const defaultLocation = {
    '@type':         'PostalAddress',
    addressCountry:  'Germany',
    addressRegion:   'Bavaria',
    addressLocality: 'Munich',
    postalCode:      '81379',
    streetAddress:   'Zielstattstr. 13'
};

export const defaultsIn = {
    en: {
        fleetsterEmail:       '<EMAIL>',
        fleetsterDescription: 'Software for Fleet Management, CarSharing and Rental.',
        fleetsterUrl:         _.find(languages, {label: 'EN'}).href,
        fleetsterLocation:    defaultLocation
    },
    de: {
        fleetsterEmail:       '<EMAIL>',
        fleetsterDescription: 'Software für Flottenmanagement, CarSharing und Vermietung.',
        fleetsterUrl:         _.find(languages, {label: 'DE'}).href,
        fleetsterLocation:    {
            ...defaultLocation,
            addressCountry:  'Deutschland',
            addressLocality: 'München'
        }
    },
    nl: {
        fleetsterEmail:       '<EMAIL>',
        fleetsterDescription: 'Software voor Vlootmanagement, CarSharing en Autoverhuur.',
        fleetsterUrl:         _.find(languages, {label: 'NL'}).href,
        fleetsterLocation:    defaultLocation
    },
    es: {
        fleetsterEmail:       '<EMAIL>',
        fleetsterDescription: 'Software para gestión de flotas, CarSharing y alquiler.',
        fleetsterUrl:         _.find(languages, {label: 'ES'}).href,
        fleetsterLocation:    defaultLocation
    }
};

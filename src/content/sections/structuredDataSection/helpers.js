import _ from 'lodash';
import {useRouter} from 'next/router';

import {getImageSource} from 'rf-components/image/helpers';

import {typeDictionary} from './constants';
import {defaultBrand, defaultManufacturer} from './defaults';
import {config} from '../../../../config';

function getKeyType(key) {
    if (typeDictionary[key]) {
        return typeDictionary[key];
    }

    return _.upperFirst(key);
}

function getCurrentUrl() {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const router = useRouter();

    return `${config.host.url}${router.asPath}`;
}

function getSingularKey(key) {
    if (_.isString(key) && key.endsWith('s')) {
        return key.slice(0, -1);
    }

    return key;
}

function transformSingleOffer(structuredData) {
    if (_.size(_.get(structuredData, 'offers.offers')) === 1) {
        const singleOffer = _.get(structuredData, 'offers.offers[0]');

        _.set(structuredData, 'offers.@type', 'Offer');
        _.set(structuredData, 'offers.price', _.get(singleOffer, 'price'));
        _.set(structuredData, 'offers.priceCurrency', _.get(singleOffer, 'priceCurrency'));

        _.unset(structuredData, 'offers.offers');
    }
}

function setAggregateOfferFields(structuredData) {
    const offers = _.get(structuredData, 'offers.offers');

    if (_.get(structuredData, 'offers.@type') === 'AggregateOffer') {
        const pricesWithCurrency = _.map(offers, offer => ({
            price:    parseFloat(_.get(offer, 'price', 0)),
            currency: _.get(offer, 'priceCurrency')
        }));

        const lowestPriceOffer = _.minBy(pricesWithCurrency, 'price');
        const lowPrice = lowestPriceOffer.price;
        const lowPriceCurrency = lowestPriceOffer.currency;

        _.set(structuredData, 'offers.lowPrice', String(lowPrice));
        _.set(structuredData, 'offers.priceCurrency', lowPriceCurrency);
        _.set(structuredData, 'offers.offerCount', offers.length);
    }

    return structuredData;
}

function handleArray(structuredData, key, value) {
    const parsedArray = value.map(item => {
        const parsedItem = parseStructuredData(item);

        return _.set(parsedItem, '@type', getKeyType(getSingularKey(key)));
    });

    return _.set(structuredData, key, parsedArray);
}

function handleObject(structuredData, key, value) {
    const parsedValue = parseStructuredData(value);

    if (!_.isEmpty(parsedValue)) {
        _.set(parsedValue, '@type', getKeyType(key));
        _.set(structuredData, key, parsedValue);
    }
}

export function parseStructuredData(rawData, structuredData = {}) {
    _.forEach(rawData, (value, key) => {
        if (['index', '_type', '_key'].includes(key)) {
            return structuredData;
        }

        if (_.isString(value)) {
            return _.set(structuredData, key, value);
        }

        if (_.get(value, '_type') === 'image') {
            return _.set(structuredData, key, getImageSource({image: value}));
        }

        if (_.isArray(value) && !_.isEmpty(value)) {
            return handleArray(structuredData, key, value);
        }

        if (_.isObject(value) && !_.isEmpty(value)) {
            handleObject(structuredData, key, value);
        }

        return structuredData;
    });
    if (_.has(structuredData, 'url') && !_.get(structuredData, 'url')) {
        const url = getCurrentUrl();

        return _.set(structuredData, 'url', url);
    }

    transformSingleOffer(structuredData);
    setAggregateOfferFields(structuredData);

    return structuredData;
}

export function setDefaultStructuredData(structuredData) {
    if (!structuredData.brand) {
        _.set(structuredData, 'brand', defaultBrand);
    }
    if (!structuredData.manufacturer) {
        _.set(structuredData, 'manufacturer', defaultManufacturer);
    }
}

import _ from 'lodash';
import React from 'react';
import Head from 'next/head';
import PropTypes from 'prop-types';

import {parseStructuredData, setDefaultStructuredData} from './helpers';

export function ProductStructuredData(props) {
    if (_.isEmpty(props)) {
        return null;
    }

    let productStructuredData = {
        '@context': 'https://schema.org',
        '@type':    'Product'
    };

    parseStructuredData(props, productStructuredData);
    setDefaultStructuredData(productStructuredData);

    const structureDataHtml = {
        __html: JSON.stringify(productStructuredData)
    };

    return (
        <Head>
            <script dangerouslySetInnerHTML={structureDataHtml} type={'application/ld+json'}/>
        </Head>
    );
}

ProductStructuredData.propTypes = {
    content: PropTypes.object
};

import {useState} from 'react';
import _ from 'lodash';

export function useHorizontalSwipeEvent({
    minSwipeDistance = 50,
    onLeftSwipe,
    onRightSwipe
}) {
    const [startPosition, setStartPosition] = useState(null);
    const [endPosition, setEndPosition] = useState(null);

    const onStart = clientX => setStartPosition(clientX);

    const onMove = clientX => startPosition && setEndPosition(clientX);

    const onEnd = () => {
        if (!startPosition || !endPosition) { return false; }

        const distance = startPosition - endPosition;
        const isLeftSwipe = distance > minSwipeDistance;
        const isRightSwipe = distance < -minSwipeDistance;

        if (isLeftSwipe && _.isFunction(onLeftSwipe)) {
            onLeftSwipe();
        } else if (isRightSwipe && _.isFunction(onRightSwipe)) {
            onRightSwipe();
        } else {
            setEndPosition(null);
        }

        return true;
    };

    const onDragStart = e => e.preventDefault();
    const onClick = e => {
        if (endPosition) { e.preventDefault(); }

        return !endPosition;
    };

    return {
        onTouchStart: e => onStart(e.targetTouches[0].clientX),
        onTouchMove:  e => onMove(e.targetTouches[0].clientX),
        onTouchEnd:   () => onEnd() && setEndPosition(null),
        onMouseDown:  e => onStart(e.clientX),
        onMouseMove:  e => onMove(e.clientX),
        onMouseUp:    () => onEnd(),
        onDragStart,
        onClick
    };
}

import React, {Children, useEffect} from 'react';
import css from 'styled-jsx/css';
import PropTypes from 'prop-types';

import {screenSizes} from 'rf-styles';

import {useHorizontalSwipeEvent} from './carouselHooks';
import {useCarouselContext} from './carouselContext';

const carouselStyles = css`
  .carousel-container {
    display: flex;
    position: relative;
    width: 100%;
    overflow-x: hidden;
    margin-bottom: 80px;
  }

  @media (min-width: ${screenSizes.xs}px) {
    .carousel-container {
      margin-bottom: 150px;
    }
  }

  @media (min-width: ${screenSizes.s}px) {
    .carousel-container {
      margin-bottom: 0;
    }
  }

  .carousel-items {
    display: flex;
    width: 100%;
    transition: transform 0.5s ease-in-out;
  }

  .carousel-item {
    min-width: 100%;
  }
`;

export function Carousel({children}) {
    const {goToNext, goToPrev, currentIndex, intervalMs} = useCarouselContext();
    const touchHandlers = useHorizontalSwipeEvent({
        onLeftSwipe:  goToNext,
        onRightSwipe: goToPrev
    });
    // the required distance between touchStart and touchEnd to be detected as a swipe

    useEffect(() => {
        const interval = setInterval(goToNext, intervalMs);

        return () => clearInterval(interval);
    }, [currentIndex, intervalMs]);

    return (
        <div className={'carousel-container'} {...touchHandlers}>
            <div className={'carousel-items'} style={{transform: `translateX(-${currentIndex * 100}%)`}}>
                {Children.map(children, (child, index) => <div key={index} className={'carousel-item'}> {child} </div>)}
            </div>
            <style jsx>{carouselStyles}</style>
        </div>
    );
}

Carousel.propTypes = {children: PropTypes.array};

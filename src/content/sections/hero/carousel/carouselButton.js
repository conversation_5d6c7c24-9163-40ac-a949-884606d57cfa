/* eslint-disable max-lines */
import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, fontWeights, hSpacing, screenSizes, vSpacing} from 'rf-styles';
import {SVGIcon} from 'rf-components';

import {ICON_SIZE} from '../constants';
import {useCarouselContext} from './carouselContext';

const styles = css`
  .carousel-button {
    overflow: hidden;
    margin-left: ${hSpacing.xxs}px;
    background-color: ${colors.darkBlue};
    color: ${colors.white};
    border: none;

    cursor: pointer;
    max-width: 10px;
    
    height: 10px;
    padding: 0;
    border-radius: 50%;
    background-color: ${colors.white};
  }
  
  @media (min-width: ${screenSizes.s}px) { 
    .carousel-button {
        width: 100%;
        max-width: unset;
        height: ${ICON_SIZE}px;
        margin-top: ${vSpacing.xs}px;
        margin-left: 0;
        float: right;
        transition: width 500ms;
        border-radius: 0;
        height: ${ICON_SIZE}px;
        padding: ${hSpacing.xs - 2}px ${hSpacing.xs}px;
        background-color: ${colors.darkBlue};
    }

    .carousel-button:hover {
      background-color: ${colors.white};
      color: ${colors.darkBlue};
    }
  }

  .carousel-button[data-selected="true"] {
    background-color: ${colors.lightBlue};
  }
  
  @media (min-width: ${screenSizes.s}px) {  .carousel-button[data-selected="true"] {
    box-shadow: 0px 1px 4px 0px #5d5d5d;
    background-color: ${colors.white};
    color: ${colors.darkBlue};
    outline: none;
  }}
  @media (min-width: ${screenSizes.s}px) { .carousel-button[data-selected="true"], .carousel-button:hover {
    width: 220px;
  }}

  .button-icon {
    width: 50px;
    height: 50px;
    margin: ${vSpacing.xs}px;
    background-color: ${colors.white};
  }
  @media (min-width: ${screenSizes.s}px) {
    .carousel-button[data-selected="true"] .button-icon, .carousel-button:hover .button-icon {
      background-color: ${colors.darkBlue};
    }
  }

  @media (min-width: ${screenSizes.s}px) { .button-icon {
    width: 30px;
    height: 30px;
  }}
  @media (max-width: ${screenSizes.xs - 1}px) { .button-icon {
    display: none;
  }}

  .button-label-wrapper {
    width: 0;
    margin-left: ${hSpacing.s}px;
    text-align: left;
    font-weight: ${fontWeights.semiBold};
    font-family: "Open Sans", sans-serif;
    font-size: 20px;
    line-height: 24px;
    display: none;
    pointer-events: none;
    transition: width 500ms;
    overflow: hidden;
  }
  
  .carousel-button[data-selected="true"] .button-label-wrapper, .carousel-button:hover .button-label-wrapper {
    display: block;
  }
  @media (max-width: ${screenSizes.s - 1}px) { .carousel-button:hover .button-label-wrapper {
    display: none;
  }}
  @media (max-width: ${screenSizes.s - 1}px) { .carousel-button[data-selected="true"] .button-label-wrapper {
    position: absolute;
    display: block;
    width: auto;
    margin-left: 0;
    top: -107px;
    left: 0;
    right: 0;
    text-align: center;
    color: ${colors.white};
    font-size: 34px;
    line-height: 42px;
  }}
  @media (max-width: ${screenSizes.xs - 1}px) { .carousel-button[data-selected="true"] .button-label-wrapper {
    top: -54px;
    font-size: 26px;
    line-height: 34px;
  }}
  @media (max-width: ${screenSizes.xxs - 1}px) { .carousel-button[data-selected="true"] .button-label-wrapper {
    top: -52px;
    font-size: 20px;
    line-height: 24px;
  }}

  .button-label {
    width: 145px;
  }
  @media (max-width: ${screenSizes.s - 1}px) { .carousel-button[data-selected="true"] .button-label {
    width: auto;
  }}`;

export function CarouselButton({index, buttonIcon}) {
    const {currentIndex, goToIndex} = useCarouselContext();
    const isSelected = index === currentIndex;

    return (
        <>
            <button
                option={index}
                className={'carousel-button center-content rf-flex-1'}
                data-selected={isSelected}
                aria-label={buttonIcon.title}
                onClick={() => goToIndex(index)}>
                <SVGIcon image={buttonIcon} className={'button-icon'} styles={styles}/>
                <div className={'rf-flex-1 button-label-wrapper'}>
                    <div className={'button-label'}>
                        {buttonIcon.title}
                    </div>
                </div>
            </button>
            <style jsx>{styles}</style>
        </>
    );
}

CarouselButton.propTypes = {
    index:      PropTypes.number,
    buttonIcon: PropTypes.object
};

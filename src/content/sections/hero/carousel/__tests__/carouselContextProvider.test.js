import React from 'react';
import {CarouselProvider, useCarouselContext} from '../carouselContext';

const carouselContext = {
    currentIndex: 1,
    totalItems:   3
};

const ContextConsumer = () => {
    const {
        goToNext,
        goToPrev,
        goToNextNoLooping,
        goToPrevNoLooping,
        currentIndex
    } = useCarouselContext();

    return (
        <>
            <p>{currentIndex}</p>
            <button className={'next'} onClick={goToNext}/>
            <button className={'previous'} onClick={goToPrev}/>
            <button className={'nextNoLoop'} onClick={goToNextNoLooping}/>
            <button className={'previousNoLoop'} onClick={goToPrevNoLooping}/>
        </>
    );
};

describe('CarouselContextProvider', () => {
    let component;
    let clickNext;
    let clickPrev;

    beforeEach(() => {
        act(() => {
            component = render(<CarouselProvider context={carouselContext}>
                <ContextConsumer/>
            </CarouselProvider>);
        });
        clickNext = component.root.findByProps({className: 'next'}).props.onClick;
        clickPrev = component.root.findByProps({className: 'previous'}).props.onClick;
    });

    it('provides expected context to child elements', () => {
        const currentIndex = component.root.findByType('p').props.children;

        expect(currentIndex).toBe(1);
    });

    it('goes to next', () => {
        act(() => {
            clickNext();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(2);
    });

    it('goes to previous', () => {
        act(() => {
            clickPrev();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(0);
    });

    it('loops to first', () => {
        act(() => {
            component = render(<CarouselProvider context={{currentIndex: 2, totalItems: 3}}>
                <ContextConsumer/>
            </CarouselProvider>);
        });

        act(() => {
            component.root.findByProps({className: 'next'}).props.onClick();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(0);
    });

    it('loops to last', () => {
        act(() => {
            component = render(<CarouselProvider context={{currentIndex: 0, totalItems: 3}}>
                <ContextConsumer/>
            </CarouselProvider>);
        });

        act(() => {
            component.root.findByProps({className: 'previous'}).props.onClick();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(2);
    });

    it('goes to next using noLoop function', () => {
        act(() => {
            component.root.findByProps({className: 'nextNoLoop'}).props.onClick();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(2);
    });

    it('goes to previous using noLoop function', () => {
        act(() => {
            component.root.findByProps({className: 'previousNoLoop'}).props.onClick();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(0);
    });

    it('does not loop to last using noLoop function', () => {
        act(() => {
            component.root.findByProps({className: 'previousNoLoop'}).props.onClick();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(0);
    });

    it('does not loop to first using noLoop function', () => {
        act(() => {
            component = render(<CarouselProvider context={{currentIndex: 2, totalItems: 3}}>
                <ContextConsumer/>
            </CarouselProvider>);
        });

        act(() => {
            component.root.findByProps({className: 'nextNoLoop'}).props.onClick();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(2);
    });

    it('does not loop to last using noLoop function', () => {
        act(() => {
            component = render(<CarouselProvider context={{currentIndex: 0, totalItems: 3}}>
                <ContextConsumer/>
            </CarouselProvider>);
        });

        act(() => {
            component.root.findByProps({className: 'previousNoLoop'}).props.onClick();
        });

        const currentIndex = component.root.findByType('p').props.children;
        expect(currentIndex).toBe(0);
    });
});

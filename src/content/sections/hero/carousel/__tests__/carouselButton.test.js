import React from 'react';
import {createRoot} from 'react-dom/client';
import {act} from 'react-dom/test-utils';

import {CarouselProvider, CarouselButton, useCarouselContext} from '../';

describe('CarouselArrows', () => {
    let container;

    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);
    });

    afterEach(() => {
        document.body.removeChild(container);
        container = null;
    });

    const TestOutput = () => {
        const {currentIndex} = useCarouselContext();

        return <div id={'test-output'}>{currentIndex}</div>;
    };

    const icon = {title: 'test', _key:  '2',
        _type: 'image',
        asset: {
            _ref:  'image-f6291580450291e8e61b85db49f1307f9e89149a-24x20-png',
            _type: 'reference'
        }
    };

    test('pressing the button should set the passed in index', () => {
        act(() => {
            createRoot(container).render(
                <CarouselProvider context={{currentIndex: 0, intervalMs: 5000, totalItems: 3}}>
                    <CarouselButton index={2} buttonIcon={icon}/>
                    <TestOutput/>
                </CarouselProvider>
            );
        });

        const testOutput = container.querySelector('#test-output');

        expect(testOutput.textContent).toBe('0');

        const btn = container.querySelector('.carousel-button');

        act(() => {
            btn.dispatchEvent(new MouseEvent('click', {bubbles: true}));
        });

        expect(testOutput.textContent).toBe('2');
    });
});

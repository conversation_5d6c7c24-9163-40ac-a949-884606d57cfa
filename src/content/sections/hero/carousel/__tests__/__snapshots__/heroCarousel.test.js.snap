// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HeroCarousel renders without errors 1`] = `
<div
  className="hero-carousel"
>
  <div
    className="carousel-container"
    onClick={[Function]}
    onDragStart={[Function]}
    onMouseDown={[Function]}
    onMouseMove={[Function]}
    onMouseUp={[Function]}
    onTouchEnd={[Function]}
    onTouchMove={[Function]}
    onTouchStart={[Function]}
  >
    <div
      className="carousel-items"
      style={
        {
          "transform": "translateX(-0%)",
        }
      }
    >
      <div
        className="carousel-item"
      >
         
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="image 1"
            decoding="async"
            height={444}
            loading="lazy"
            sizes="(max-width: 362px) 361px, 533px"
            src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=361&h=301&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format 533w"
            style={
              {
                "height": "auto",
                "maxHeight": 444,
                "maxWidth": 533,
                "width": "100%",
              }
            }
            width={533}
          />
        </div>
         
      </div>
      <div
        className="carousel-item"
      >
         
         
      </div>
      <div
        className="carousel-item"
      >
         
         
      </div>
    </div>
    <style />
  </div>
  <div
    className="carousel-buttons"
    id="selector-id"
  >
    <button
      aria-label="image 1"
      className="carousel-button center-content rf-flex-1"
      data-selected={true}
      onClick={[Function]}
      option={0}
    >
      <div
        className="button-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <div
        className="rf-flex-1 button-label-wrapper"
      >
        <div
          className="button-label"
        >
          image 1
        </div>
      </div>
    </button>
    <style />
    <button
      aria-label="image 2"
      className="carousel-button center-content rf-flex-1"
      data-selected={false}
      onClick={[Function]}
      option={1}
    >
      <div
        className="button-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <div
        className="rf-flex-1 button-label-wrapper"
      >
        <div
          className="button-label"
        >
          image 2
        </div>
      </div>
    </button>
    <style />
    <button
      aria-label="image 3"
      className="carousel-button center-content rf-flex-1"
      data-selected={false}
      onClick={[Function]}
      option={2}
    >
      <div
        className="button-icon"
        role="img"
        style={
          {
            "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format)",
            "WebkitMaskPosition": "center",
            "WebkitMaskRepeat": "no-repeat",
            "WebkitMaskSize": "contain",
            "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format) no-repeat center / contain",
          }
        }
      >
        <style />
      </div>
      <div
        className="rf-flex-1 button-label-wrapper"
      >
        <div
          className="button-label"
        >
          image 3
        </div>
      </div>
    </button>
    <style />
    <style />
  </div>
  <style />
</div>
`;

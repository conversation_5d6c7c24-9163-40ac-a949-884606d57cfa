// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Hero Adds light theme class when lightTheme is true 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="sync"
        height={20}
        loading="eager"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h1
        className="h1 light-theme resolved-class-name"
      />
      <p
        className="resolved-class-name light-theme h5"
      >
         
        huhuebr
      </p>
      <div
        className="call-to-action light-theme"
      >
        <a
          className="center-content light-theme hover-enabled"
          href="link"
          rel="noreferrer"
          target="_blank"
        >
          title
          <style />
        </a>
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="image 1"
            decoding="sync"
            height={444}
            loading="eager"
            sizes="(max-width: 362px) 361px, 533px"
            src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=361&h=301&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format 533w"
            style={
              {
                "height": "auto",
                "maxHeight": 444,
                "maxWidth": 533,
                "width": "100%",
              }
            }
            width={533}
          />
        </div>
      </div>
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

exports[`Hero renders H1 if is the first element of the page 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="sync"
        height={20}
        loading="eager"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h1
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="image 1"
            decoding="sync"
            height={444}
            loading="eager"
            sizes="(max-width: 362px) 361px, 533px"
            src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=361&h=301&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format 533w"
            style={
              {
                "height": "auto",
                "maxHeight": 444,
                "maxWidth": 533,
                "width": "100%",
              }
            }
            width={533}
          />
        </div>
      </div>
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without background image 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="link"
          rel="noreferrer"
          target="_blank"
        >
          title
          <style />
        </a>
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="image 1"
            decoding="async"
            height={444}
            loading="lazy"
            sizes="(max-width: 362px) 361px, 533px"
            src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=361&h=301&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format 533w"
            style={
              {
                "height": "auto",
                "maxHeight": 444,
                "maxWidth": 533,
                "width": "100%",
              }
            }
            width={533}
          />
        </div>
      </div>
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without errors with multiple images 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content hero-carousel"
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={20}
        loading="lazy"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="link"
          rel="noreferrer"
          target="_blank"
        >
          title
          <style />
        </a>
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    />
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: 525px;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without errors with multiple images and no-amp 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content hero-carousel"
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={20}
        loading="lazy"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="link"
          rel="noreferrer"
          target="_blank"
        >
          title
          <style />
        </a>
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="hero-carousel"
      >
        <div
          className="carousel-container"
          onClick={[Function]}
          onDragStart={[Function]}
          onMouseDown={[Function]}
          onMouseMove={[Function]}
          onMouseUp={[Function]}
          onTouchEnd={[Function]}
          onTouchMove={[Function]}
          onTouchStart={[Function]}
        >
          <div
            className="carousel-items"
            style={
              {
                "transform": "translateX(-0%)",
              }
            }
          >
            <div
              className="carousel-item"
            >
               
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="image 1"
                  decoding="async"
                  height={444}
                  loading="lazy"
                  sizes="(max-width: 362px) 361px, 533px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=361&h=301&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format 533w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 444,
                      "maxWidth": 533,
                      "width": "100%",
                    }
                  }
                  width={533}
                />
              </div>
               
            </div>
            <div
              className="carousel-item"
            >
               
               
            </div>
            <div
              className="carousel-item"
            >
               
               
            </div>
          </div>
          <style />
        </div>
        <div
          className="carousel-buttons"
          id="selector-undefined"
        >
          <button
            aria-label="image 1"
            className="carousel-button center-content rf-flex-1"
            data-selected={true}
            onClick={[Function]}
            option={0}
          >
            <div
              className="button-icon"
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <div
              className="rf-flex-1 button-label-wrapper"
            >
              <div
                className="button-label"
              >
                image 1
              </div>
            </div>
          </button>
          <style />
          <button
            aria-label="image 2"
            className="carousel-button center-content rf-flex-1"
            data-selected={false}
            onClick={[Function]}
            option={1}
          >
            <div
              className="button-icon"
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <div
              className="rf-flex-1 button-label-wrapper"
            >
              <div
                className="button-label"
              >
                image 2
              </div>
            </div>
          </button>
          <style />
          <button
            aria-label="image 3"
            className="carousel-button center-content rf-flex-1"
            data-selected={false}
            onClick={[Function]}
            option={2}
          >
            <div
              className="button-icon"
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <div
              className="rf-flex-1 button-label-wrapper"
            >
              <div
                className="button-label"
              >
                image 3
              </div>
            </div>
          </button>
          <style />
          <style />
        </div>
        <style />
      </div>
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: 525px;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without errors with single image 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={20}
        loading="lazy"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="link"
          rel="noreferrer"
          target="_blank"
        >
          title
          <style />
        </a>
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="image 1"
            decoding="async"
            height={444}
            loading="lazy"
            sizes="(max-width: 362px) 361px, 533px"
            src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=361&h=301&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format 533w"
            style={
              {
                "height": "auto",
                "maxHeight": 444,
                "maxWidth": 533,
                "width": "100%",
              }
            }
            width={533}
          />
        </div>
      </div>
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without errors with single image and no amp 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={20}
        loading="lazy"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="link"
          rel="noreferrer"
          target="_blank"
        >
          title
          <style />
        </a>
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      >
        <div
          className="center-content"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
        >
          <img
            alt="image 1"
            decoding="async"
            height={444}
            loading="lazy"
            sizes="(max-width: 362px) 361px, 533px"
            src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format"
            srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=361&h=301&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=533&h=444&auto=format 533w"
            style={
              {
                "height": "auto",
                "maxHeight": 444,
                "maxWidth": 533,
                "width": "100%",
              }
            }
            width={533}
          />
        </div>
      </div>
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without hero image 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={20}
        loading="lazy"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <div
        className="call-to-action"
      >
        <a
          className="center-content  hover-enabled"
          href="link"
          rel="noreferrer"
          target="_blank"
        >
          title
          <style />
        </a>
        <style />
      </div>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      />
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without the cta if is undefined 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={20}
        loading="lazy"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      />
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

exports[`Hero renders without the cta if there is no title 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding hero-section center-content "
  id=""
>
  <div
    className="hero-bg"
  >
    <div
      className="center-content"
      style={
        {
          "height": "100%",
          "width": "100%",
        }
      }
    >
      <img
        alt="Hero Background"
        decoding="async"
        height={20}
        loading="lazy"
        sizes="24px"
        src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
        srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
        style={
          {
            "height": "100%",
            "objectFit": "cover",
            "width": "100%",
          }
        }
        width={24}
      />
    </div>
    <style>
      
                .hero-bg {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    z-index: -1;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            
    </style>
  </div>
  <div
    className="rf-flex-container"
  >
    <div
      className="resolved-class-name rf-cell rf-flex-6 hero-text"
    >
      <h2
        className="h1  resolved-class-name"
      />
      <p
        className="resolved-class-name  h5"
      >
         
        huhuebr
      </p>
      <style />
    </div>
    <div
      className="rf-cell rf-flex-6 center-content right-hero-column"
    >
      <div
        className="rf-cell rf-flex-6"
      />
    </div>
    <style />
  </div>
  <style />
  <style>
    
              @media(min-width: 1025px) { .hero-section {
                height: auto;
              }}
            
  </style>
</section>
`;

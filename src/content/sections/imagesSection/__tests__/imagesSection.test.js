import React from 'react';
import {ImagesSection} from '../';

describe('ImagesSection', () => {
    const image = {
        image: {
            _type: 'image',
            asset: {
                _ref:  'image-f6291580450291e8e61b85db49f1307f9e89149a-24x20-png',
                _type: 'reference'
            },
            height: 20,
            width:  24
        },
        alt: 'alternative text'
    };

    const svgImage = {
        image: {
            _type: 'image',
            asset: {
                _ref:  'image-c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669-svg',
                _type: 'reference'
            },
            height: 669,
            width:  604
        },
        alt: 'alternative text'
    };

    describe('default-images', () => {
        test('renders without errors with single image', () => {
            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [image],
                    title:  'normal images section'
                }]}
                minimized={false}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders with internal link', () => {
            const imageWithLink = _.assign({}, image, {route: {_ref: 'ref', _type: 'internalLink', href: ''}});

            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [imageWithLink],
                    title:  'mini images section'
                }]}
                minimized={false}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders with external link', () => {
            const imageWithLink = _.assign({}, svgImage, {link: 'https://daily.dev'});

            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [imageWithLink],
                    title:  'mini images section'
                }]}
                minimized={false}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders without errors with multiple images', () => {
            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [image, svgImage],
                    title:  'normal images section'
                }]}
                minimized={false}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });
    });

    describe('mini-images', () => {
        test('renders without errors with single image content', () => {
            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [svgImage],
                    title:  'mini images section'
                }]}
                minimized={true}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders the no-title alternative when there is no title', () => {
            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [svgImage],
                    title:  ''
                }]}
                minimized={true}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders with internal link', () => {
            const imageWithLink = _.assign({}, svgImage, {route: {_ref: 'ref', _type: 'internalLink', href: ''}});

            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [imageWithLink],
                    title:  'mini images section'
                }]}
                minimized={true}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders with external link', () => {
            const imageWithLink = _.assign({}, svgImage, {link: 'https://daily.dev'});

            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [imageWithLink],
                    title:  'mini images section'
                }]}
                minimized={true}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders with bold title when it contains text between asterisks', () => {
            const component = render(<ImagesSection
                content={[{
                    _key:   '18ffc58bdc0e',
                    images: [svgImage],
                    title:  'mini *images* section'
                }]}
                minimized={true}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });

        test('renders without errors with multiple image contents', () => {
            const component = render(<ImagesSection
                content={[{
                    _key:   'firstcontent',
                    images: [svgImage],
                    title:  'left mini images'
                }, {
                    _key:   'secondcontent',
                    images: [image],
                    title:  'right mini images'
                }]}
                minimized={true}
                index={0}
            />);

            expect(component).toMatchSnapshot();
        });
    });
});

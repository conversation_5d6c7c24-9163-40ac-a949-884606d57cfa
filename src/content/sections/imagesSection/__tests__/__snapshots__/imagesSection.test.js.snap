// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ImagesSection default-images renders with external link 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div>
      <div
        className="center-content title-container"
      >
        <h1
          className="h1"
          style={
            {
              "color": "#1D88BB",
            }
          }
        >
          mini images section
        </h1>
        <style />
        <style>
          
              .title-container:global(.h1) {
                margin-top: 0;
                margin-bottom: 0;
                padding: 0;
              }
        </style>
      </div>
      <div
        className="regular-images"
      >
        <div
          className="center-content image-spacing"
        >
          <div
            className="section-image-container"
          >
            <a
              href="https://daily.dev"
              rel="noreferrer"
              target="_blank"
            >
              <div
                aria-label="alternative text"
                className="section-image image-with-ref"
                role="img"
                style={
                  {
                    "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format)",
                    "WebkitMaskPosition": "center",
                    "WebkitMaskRepeat": "no-repeat",
                    "WebkitMaskSize": "contain",
                    "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format) no-repeat center / contain",
                  }
                }
              >
                <style />
              </div>
            </a>
          </div>
          <style />
          <style>
            
                @media (min-width: 1281px) {
                    .section-image-container {
                        width: 604px;
                        height: 669px;
                    }
                }
            
          </style>
        </div>
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection default-images renders with internal link 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div>
      <div
        className="center-content title-container"
      >
        <h1
          className="h1"
          style={
            {
              "color": "#1D88BB",
            }
          }
        >
          mini images section
        </h1>
        <style />
        <style>
          
              .title-container:global(.h1) {
                margin-top: 0;
                margin-bottom: 0;
                padding: 0;
              }
        </style>
      </div>
      <div
        className="regular-images"
      >
        <div
          className="center-content image-spacing"
        >
          <div
            className="section-image-container"
          >
            <a
              href=""
            >
              <div
                className="center-content"
                style={
                  {
                    "height": "100%",
                    "width": "100%",
                  }
                }
              >
                <img
                  alt="alternative text"
                  decoding="async"
                  height={20}
                  loading="lazy"
                  sizes="24px"
                  src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
                  srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
                  style={
                    {
                      "height": "auto",
                      "maxHeight": 20,
                      "maxWidth": 24,
                      "width": "100%",
                    }
                  }
                  width={24}
                />
              </div>
            </a>
          </div>
          <style />
          <style>
            
                @media (min-width: 1281px) {
                    .section-image-container {
                        width: 24px;
                        height: 20px;
                    }
                }
            
          </style>
        </div>
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection default-images renders without errors with multiple images 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div>
      <div
        className="center-content title-container"
      >
        <h1
          className="h1"
          style={
            {
              "color": "#1D88BB",
            }
          }
        >
          normal images section
        </h1>
        <style />
        <style>
          
              .title-container:global(.h1) {
                margin-top: 0;
                margin-bottom: 0;
                padding: 0;
              }
        </style>
      </div>
      <div
        className="regular-images"
      >
        <div
          className="center-content image-spacing"
        >
          <div
            className="section-image-container"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="alternative text"
                decoding="async"
                height={20}
                loading="lazy"
                sizes="24px"
                src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 20,
                    "maxWidth": 24,
                    "width": "100%",
                  }
                }
                width={24}
              />
            </div>
          </div>
          <style />
          <style>
            
                @media (min-width: 1281px) {
                    .section-image-container {
                        width: 24px;
                        height: 20px;
                    }
                }
            
          </style>
        </div>
        <div
          className="center-content image-spacing"
        >
          <div
            className="section-image-container"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="alternative text"
                decoding="async"
                height={669}
                loading="lazy"
                sizes="(max-width: 362px) 361px, 604px"
                src="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=361&h=400&auto=format 361w, https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?w=604&h=669&auto=format 604w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 669,
                    "maxWidth": 604,
                    "width": "100%",
                  }
                }
                width={604}
              />
            </div>
          </div>
          <style />
          <style>
            
                @media (min-width: 1281px) {
                    .section-image-container {
                        width: 604px;
                        height: 669px;
                    }
                }
            
          </style>
        </div>
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection default-images renders without errors with single image 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div>
      <div
        className="center-content title-container"
      >
        <h1
          className="h1"
          style={
            {
              "color": "#1D88BB",
            }
          }
        >
          normal images section
        </h1>
        <style />
        <style>
          
              .title-container:global(.h1) {
                margin-top: 0;
                margin-bottom: 0;
                padding: 0;
              }
        </style>
      </div>
      <div
        className="regular-images"
      >
        <div
          className="center-content image-spacing"
        >
          <div
            className="section-image-container"
          >
            <div
              className="center-content"
              style={
                {
                  "height": "100%",
                  "width": "100%",
                }
              }
            >
              <img
                alt="alternative text"
                decoding="async"
                height={20}
                loading="lazy"
                sizes="24px"
                src="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format"
                srcSet="https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?w=24&h=20&auto=format 24w"
                style={
                  {
                    "height": "auto",
                    "maxHeight": 20,
                    "maxWidth": 24,
                    "width": "100%",
                  }
                }
                width={24}
              />
            </div>
          </div>
          <style />
          <style>
            
                @media (min-width: 1281px) {
                    .section-image-container {
                        width: 24px;
                        height: 20px;
                    }
                }
            
          </style>
        </div>
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection mini-images renders the no-title alternative when there is no title 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div
      className="mini-images-container"
    >
      <div
        className="mini-images-flex-content"
      >
        <div
          className="mini-images"
        >
          <div
            className="mini-section-image"
          >
            <div
              aria-label="alternative text"
              className="mini-image "
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <style>
              
                .mini-section-image {
                    width: 22.57100149476831px;
                    height: 25px;
                }
                @media (max-width: 1281px) {
                    .mini-section-image {
                        width: 18.05680119581465px;
                        height: 20px;
                    }
                }
            
            </style>
            <style />
          </div>
        </div>
        <style />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection mini-images renders with bold title when it contains text between asterisks 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div
      className="mini-images-container"
    >
      <div
        className="mini-images-flex-content"
      >
        <div
          className="mini-images-title"
        >
          mini *images* section
        </div>
        <div
          className="mini-images"
        >
          <div
            className="mini-section-image"
          >
            <div
              aria-label="alternative text"
              className="mini-image "
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <style>
              
                .mini-section-image {
                    width: 22.57100149476831px;
                    height: 25px;
                }
                @media (max-width: 1281px) {
                    .mini-section-image {
                        width: 18.05680119581465px;
                        height: 20px;
                    }
                }
            
            </style>
            <style />
          </div>
        </div>
        <style />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection mini-images renders with external link 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div
      className="mini-images-container"
    >
      <div
        className="mini-images-flex-content"
      >
        <div
          className="mini-images-title"
        >
          mini images section
        </div>
        <div
          className="mini-images"
        >
          <div
            className="mini-section-image"
          >
            <a
              href="https://daily.dev"
              rel="noreferrer"
              target="_blank"
            >
              <div
                aria-label="alternative text"
                className="mini-image mini-image-hover"
                role="img"
                style={
                  {
                    "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format)",
                    "WebkitMaskPosition": "center",
                    "WebkitMaskRepeat": "no-repeat",
                    "WebkitMaskSize": "contain",
                    "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format) no-repeat center / contain",
                  }
                }
              >
                <style />
              </div>
            </a>
            <style>
              
                .mini-section-image {
                    width: 22.57100149476831px;
                    height: 25px;
                }
                @media (max-width: 1281px) {
                    .mini-section-image {
                        width: 18.05680119581465px;
                        height: 20px;
                    }
                }
            
            </style>
            <style />
          </div>
        </div>
        <style />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection mini-images renders with internal link 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div
      className="mini-images-container"
    >
      <div
        className="mini-images-flex-content"
      >
        <div
          className="mini-images-title"
        >
          mini images section
        </div>
        <div
          className="mini-images"
        >
          <div
            className="mini-section-image"
          >
            <a
              href=""
            >
              <div
                aria-label="alternative text"
                className="mini-image mini-image-hover"
                role="img"
                style={
                  {
                    "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format)",
                    "WebkitMaskPosition": "center",
                    "WebkitMaskRepeat": "no-repeat",
                    "WebkitMaskSize": "contain",
                    "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format) no-repeat center / contain",
                  }
                }
              >
                <style />
              </div>
            </a>
            <style>
              
                .mini-section-image {
                    width: 22.57100149476831px;
                    height: 25px;
                }
                @media (max-width: 1281px) {
                    .mini-section-image {
                        width: 18.05680119581465px;
                        height: 20px;
                    }
                }
            
            </style>
            <style />
          </div>
        </div>
        <style />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection mini-images renders without errors with multiple image contents 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div
      className="mini-images-container"
    >
      <div
        className="mini-images-flex-content"
      >
        <div
          className="mini-images-title"
        >
          left mini images
        </div>
        <div
          className="mini-images"
        >
          <div
            className="mini-section-image"
          >
            <div
              aria-label="alternative text"
              className="mini-image "
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <style>
              
                .mini-section-image {
                    width: 22.57100149476831px;
                    height: 25px;
                }
                @media (max-width: 1281px) {
                    .mini-section-image {
                        width: 18.05680119581465px;
                        height: 20px;
                    }
                }
            
            </style>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="mini-images-flex-content"
      >
        <div
          className="mini-images-title"
        >
          right mini images
        </div>
        <div
          className="mini-images"
        >
          <div
            className="mini-section-image"
          >
            <div
              aria-label="alternative text"
              className="mini-image "
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/f6291580450291e8e61b85db49f1307f9e89149a-24x20.png?auto=format) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <style>
              
                .mini-section-image {
                    width: 30px;
                    height: 25px;
                }
                @media (max-width: 1281px) {
                    .mini-section-image {
                        width: 24px;
                        height: 20px;
                    }
                }
            
            </style>
            <style />
          </div>
        </div>
        <style />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`ImagesSection mini-images renders without errors with single image content 1`] = `
<section
  className="page-horizontal-padding"
>
  <div
    className="images-section"
  >
    <div
      className="mini-images-container"
    >
      <div
        className="mini-images-flex-content"
      >
        <div
          className="mini-images-title"
        >
          mini images section
        </div>
        <div
          className="mini-images"
        >
          <div
            className="mini-section-image"
          >
            <div
              aria-label="alternative text"
              className="mini-image "
              role="img"
              style={
                {
                  "WebkitMaskImage": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format)",
                  "WebkitMaskPosition": "center",
                  "WebkitMaskRepeat": "no-repeat",
                  "WebkitMaskSize": "contain",
                  "mask": "url(https://cdn.sanity.io/images/dp11egz7/development/c3ff2ba4fdc0faac4842db7f7e41d73533b5d734-604x669.svg?auto=format) no-repeat center / contain",
                }
              }
            >
              <style />
            </div>
            <style>
              
                .mini-section-image {
                    width: 22.57100149476831px;
                    height: 25px;
                }
                @media (max-width: 1281px) {
                    .mini-section-image {
                        width: 18.05680119581465px;
                        height: 20px;
                    }
                }
            
            </style>
            <style />
          </div>
        </div>
        <style />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

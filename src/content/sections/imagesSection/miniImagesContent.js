import React from 'react';
import _ from 'lodash';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, hSpacing, screenSizes} from 'rf-styles';
import {MiniSectionImage} from './miniSectionImage';

const styles = css`
  .mini-images-flex-content {
    display: block;
  }

  @media (min-width: ${screenSizes.s}px) {
    .mini-images-flex-content {
      display: flex;
      justify-content: space-around;
    }
  }

  .mini-images-title {
    color: ${colors.blue};
    font-size: 12px;
    line-height: 12px;
    display: flex;
    align-items: center;
    margin-bottom: ${hSpacing.s}px;
    padding-right: 0;
    justify-content: center;
  }

  @media (min-width: ${screenSizes.s}px) {
    .mini-images-title {
      padding-right: ${hSpacing.m}px;
      justify-content: unset;
      margin-bottom: inherit;
    }
  }

  .mini-images {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  @media (min-width: ${screenSizes.s}px) {
    .mini-images {
      flex-wrap: unset;
      justify-content: unset;
    }
  }
`;

export function MiniImagesContent({images, title, index}) {
    return (
        <div className={'mini-images-flex-content'}>
            {title && <div className={'mini-images-title'}>{title}</div> }
            <div className={'mini-images'}>
                {_.map(images, image => (
                    <MiniSectionImage
                        key={`${_.get(image, 'image.asset._ref')}-${index}`}
                        image={image.image}
                        alt={image.alt}
                        link={image.link}
                        route={image.route}
                    />
                ))}
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

MiniImagesContent.propTypes = {
    images:    PropTypes.array,
    className: PropTypes.string,
    title:     PropTypes.string,
    index:     PropTypes.number
};

import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, vSpacing, screenSizes} from 'rf-styles';

import {ImageElement} from './ImageElement';

const styles = css`
.image-spacing {
    padding-bottom: ${vSpacing.s}px;
}

.section-image {
    width: 100%;
    height: 100%;
    background-color: ${colors.gray};
}
    
.section-image.image-with-ref:hover {
    background-color: ${colors.blue};
}`;

export function SectionImage({image, alt, link, route}) {
    const isSVG = image.asset._ref.includes('-svg') && !!(link || route);
    const {width, height} = image;

    return (
        <div className={'center-content image-spacing'}>
            <div className={'section-image-container'}>
                <ImageElement image={image}
                    alt={alt}
                    link={link}
                    route={route}
                    className={`section-image ${(link || route || '') && 'image-with-ref'}`}
                    isSVG={isSVG}
                    styles={styles}/>
            </div>
            <style jsx>{styles}</style>
            <style jsx>{`
                @media (min-width: ${screenSizes.m}px) {
                    .section-image-container {
                        width: ${width}px;
                        height: ${height}px;
                    }
                }
            `}
            </style>
        </div>
    );
}

SectionImage.propTypes = {
    image: PropTypes.object,
    alt:   PropTypes.string,
    link:  PropTypes.string,
    route: PropTypes.shape({
        _ref: PropTypes.string
    })
};

import React from 'react';
import PropTypes from 'prop-types';

import {Image, SVGIcon} from 'rf-components';

export function ImageElement({image, alt, link, route, styles, className, isSVG}) {
    const Component = isSVG
        ? <SVGIcon image={image} alt={alt} styles={styles} className={className}/>
        : <Image image={image} imageAlt={alt} width={image.width} height={image.height}/>;

    if (route) {
        return <a key={image._key} href={route?.href}>{Component}</a>;
    }
    if (link) {
        return <a href={link} target={'_blank'} rel={'noreferrer'}>{Component}</a>;
    }

    return Component;
}

ImageElement.propTypes = {
    className: PropTypes.string,
    styles:    PropTypes.oneOfType([
        PropTypes.object,
        PropTypes.string
    ]),
    image: PropTypes.object,
    alt:   PropTypes.string,
    link:  PropTypes.string,
    isSVG: PropTypes.bool,
    route: PropTypes.shape({
        _ref: PropTypes.string
    })
};

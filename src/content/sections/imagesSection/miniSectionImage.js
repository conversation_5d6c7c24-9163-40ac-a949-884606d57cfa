import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {colors, hSpacing, screenSizes} from 'rf-styles';

import {ImageElement} from './ImageElement';
import {MINI_IMAGE_HEIGHT, MINI_IMAGE_HEIGHT_SMALL} from './constants';

const styles = css`
  .mini-image {
    height: 100%;
    width: 100%;
    background-color: ${colors.gray};
  }

  .mini-image-hover:hover {
    background-color: ${colors.lightBlue};
  }

  .mini-section-image {
    margin: 0 ${hSpacing.sm}px 0 0;
  }

  @media (min-width: ${screenSizes.s}px) {
    .mini-section-image {
      margin: 0 ${hSpacing.m}px 0 0;
    }
  }
`;

export function MiniSectionImage({image, alt, link, route}) {
    const {width, height} = image;
    const aspectRatio = width / height;
    const imageWidth = MINI_IMAGE_HEIGHT * aspectRatio;
    const imageWidthSmall = MINI_IMAGE_HEIGHT_SMALL * aspectRatio;

    const imageClassName = `mini-image ${link || route ? 'mini-image-hover' : ''}`;

    return (
        <div className={'mini-section-image'}>
            <ImageElement image={image} alt={alt} link={link} route={route} className={imageClassName} isSVG={true} styles={styles}/>
            <style jsx>{`
                .mini-section-image {
                    width: ${imageWidth}px;
                    height: ${MINI_IMAGE_HEIGHT}px;
                }
                @media (max-width: ${screenSizes.m}px) {
                    .mini-section-image {
                        width: ${imageWidthSmall}px;
                        height: ${MINI_IMAGE_HEIGHT_SMALL}px;
                    }
                }
            `}
            </style>
            <style jsx>{styles}</style>
        </div>
    );
}

MiniSectionImage.propTypes = {
    image: PropTypes.object,
    alt:   PropTypes.string,
    link:  PropTypes.string,
    route: PropTypes.object
};

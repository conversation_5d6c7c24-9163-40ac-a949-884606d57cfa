import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {CallToAction, SimpleBlockContent, MainHeading} from 'rf-components';
import {hSpacing, screenSizes} from 'rf-styles';

const styles = css`
  .text-section {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .text-section > p {
    margin-bottom: 5px;
  }

  .text-section :global(.call-to-action) {
    width: 100%;
  }

  @media (min-width: ${screenSizes.s}px) {
    .text-section {
      width: 50%;
      padding: 0 ${hSpacing.m}px;
    }

    .text-section :global(.call-to-action) {
      width: unset;
    }
  }

  @media (min-width: ${screenSizes.l}px) {
    .text-section {
      width: ${7 / 12 * 100}%
    }
  }

  .text-section > div {
    width: 100%
  }
`;

export function Text({heading = '', content = [], index, callToAction = {}}) {
    return (
        <div className={'rf-cell text-section'}>
            <div>
                {heading && <MainHeading index={index} heading={heading} classNames={'h2'}/>}
                <SimpleBlockContent blocks={content}/>
                {callToAction.title &&
                <CallToAction {...callToAction}/>
                }
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

Text.propTypes = {
    heading:      PropTypes.string,
    content:      PropTypes.array,
    callToAction: PropTypes.object,
    index:        PropTypes.number
};

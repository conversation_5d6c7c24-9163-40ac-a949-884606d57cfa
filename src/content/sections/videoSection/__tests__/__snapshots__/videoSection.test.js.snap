// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`videoSection Correctly loads vimeo video 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <div
    className="rf-flex-container center-content reverse-section"
  >
    <div
      className="rf-cell text-section"
    >
      <div>
        <h2
          className="h2"
        >
          huehuebr
        </h2>
        <div />
        <div
          className="call-to-action"
        >
          <a
            className="center-content  hover-enabled"
            href="link"
            rel="noreferrer"
            target="_blank"
          >
            title
            <style />
          </a>
          <style />
        </div>
      </div>
      <style />
    </div>
    <div
      className="video"
    >
      <iframe
        allow="autoplay; fullscreen"
        allowFullScreen={true}
        frameBorder="0"
        src="https://player.vimeo.com/video/1212431?autoplay=1&loop=1&autopause=0&muted=1"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
        title="huehuebr"
      />
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`videoSection Correctly parses and loads vimeo url with slash at the end 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <div
    className="rf-flex-container center-content reverse-section"
  >
    <div
      className="rf-cell text-section"
    >
      <div>
        <h2
          className="h2"
        >
          huehuebr
        </h2>
        <div />
        <div
          className="call-to-action"
        >
          <a
            className="center-content  hover-enabled"
            href="link"
            rel="noreferrer"
            target="_blank"
          >
            title
            <style />
          </a>
          <style />
        </div>
      </div>
      <style />
    </div>
    <div
      className="video"
    >
      <iframe
        allow="autoplay; fullscreen"
        allowFullScreen={true}
        frameBorder="0"
        src="https://player.vimeo.com/video/1212431?autoplay=1&loop=1&autopause=0&muted=1"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
        title="huehuebr"
      />
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`videoSection Correctly parses and loads youtube url 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <div
    className="rf-flex-container center-content reverse-section"
  >
    <div
      className="rf-cell text-section"
    >
      <div>
        <h2
          className="h2"
        >
          huehuebr
        </h2>
        <div />
        <div
          className="call-to-action"
        >
          <a
            className="center-content  hover-enabled"
            href="link"
            rel="noreferrer"
            target="_blank"
          >
            title
            <style />
          </a>
          <style />
        </div>
      </div>
      <style />
    </div>
    <div
      className="video"
    >
      <iframe
        allow="autoplay; fullscreen"
        allowFullScreen={true}
        frameBorder="0"
        src="https://www.youtube.com/embed/1234567?playlist=1234567&autoplay=1&mute=1&loop=1"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
        title="huehuebr"
      />
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`videoSection Correctly parses and loads youtube url with extra params 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <div
    className="rf-flex-container center-content reverse-section"
  >
    <div
      className="rf-cell text-section"
    >
      <div>
        <h2
          className="h2"
        >
          huehuebr
        </h2>
        <div />
        <div
          className="call-to-action"
        >
          <a
            className="center-content  hover-enabled"
            href="link"
            rel="noreferrer"
            target="_blank"
          >
            title
            <style />
          </a>
          <style />
        </div>
      </div>
      <style />
    </div>
    <div
      className="video"
    >
      <iframe
        allow="autoplay; fullscreen"
        allowFullScreen={true}
        frameBorder="0"
        src="https://www.youtube.com/embed/1234567?playlist=1234567&autoplay=1&mute=1&loop=1"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
        title="huehuebr"
      />
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`videoSection renders without errors 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <div
    className="rf-flex-container center-content reverse-section"
  >
    <div
      className="rf-cell text-section"
    >
      <div>
        <h2
          className="h2"
        >
          huehuebr
        </h2>
        <div />
        <div
          className="call-to-action"
        >
          <a
            className="center-content  hover-enabled"
            href="link"
            rel="noreferrer"
            target="_blank"
          >
            title
            <style />
          </a>
          <style />
        </div>
      </div>
      <style />
    </div>
    <div
      className="video"
    >
      <iframe
        allow="autoplay; fullscreen"
        allowFullScreen={true}
        frameBorder="0"
        src="https://player.vimeo.com/video/1212431?autoplay=1&loop=1&autopause=0&muted=1"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
        title="huehuebr"
      />
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`videoSection renders without errors with no callToAction 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id="huehuebr"
>
  <div
    className="rf-flex-container center-content reverse-section"
  >
    <div
      className="rf-cell text-section"
    >
      <div>
        <h2
          className="h2"
        >
          huehuebr
        </h2>
        <div />
      </div>
      <style />
    </div>
    <div
      className="video"
    >
      <iframe
        allow="autoplay; fullscreen"
        allowFullScreen={true}
        frameBorder="0"
        src="https://player.vimeo.com/video/1212431?autoplay=1&loop=1&autopause=0&muted=1"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
        title="huehuebr"
      />
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`videoSection renders without errors with no heading or content 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id=""
>
  <div
    className="rf-flex-container center-content reverse-section"
  >
    <div
      className="rf-cell text-section"
    >
      <div>
        <div
          className="call-to-action"
        >
          <a
            className="center-content  hover-enabled"
            href="link"
            rel="noreferrer"
            target="_blank"
          >
            title
            <style />
          </a>
          <style />
        </div>
      </div>
      <style />
    </div>
    <div
      className="video"
    >
      <iframe
        allow="autoplay; fullscreen"
        allowFullScreen={true}
        frameBorder="0"
        src="https://player.vimeo.com/video/1212431?autoplay=1&loop=1&autopause=0&muted=1"
        style={
          {
            "height": "100%",
            "width": "100%",
          }
        }
        title="Video content"
      />
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`videoSection writes correct structuredData 1`] = `
[
  <section
    className="page-horizontal-padding section-vertical-padding"
    id="huehuebr"
  >
    <div
      className="rf-flex-container center-content reverse-section"
    >
      <div
        className="rf-cell text-section"
      >
        <div>
          <h2
            className="h2"
          >
            huehuebr
          </h2>
          <div />
          <div
            className="call-to-action"
          >
            <a
              className="center-content  hover-enabled"
              href="link"
              rel="noreferrer"
              target="_blank"
            >
              title
              <style />
            </a>
            <style />
          </div>
        </div>
        <style />
      </div>
      <div
        className="video"
      >
        <iframe
          allow="autoplay; fullscreen"
          allowFullScreen={true}
          frameBorder="0"
          src="https://player.vimeo.com/video/1212431?autoplay=1&loop=1&autopause=0&muted=1"
          style={
            {
              "height": "100%",
              "width": "100%",
            }
          }
          title="huehuebr"
        />
        <style />
      </div>
    </div>
    <style />
  </section>,
  <div>
    <script
      dangerouslySetInnerHTML={
        {
          "__html": "{"@context":"https://schema.org","@type":"VideoObject","name":"Keanu Reeves Plays With Puppies While Answering Fan Questions","description":"Keanu Reeves plays with puppies and answers questions","uploadDate":"2019-05-17T15:00:13Z","thumbnailUrl":"https://i.ytimg.com/vi/rOqUiXhECos/sddefault.jpg","duration":"PT5M27S","embedUrl":"https://www.youtube.com/embed/rOqUiXhECos","interactionStatistic":{"@type":"InteractionCounter","interactionType":{"@type":"http://schema.org/WatchAction"},"userInteractionCount":18030191}}",
        }
      }
      type="application/ld+json"
    />
  </div>,
]
`;

import React, {useState, useEffect, useRef} from 'react';
import PropTypes from 'prop-types';
import {css} from 'styled-jsx/css';

import {screenSizes, vSpacing} from 'rf-styles';
import {parseVideoUrl} from 'rf-helpers';

// 16:9 aspect ratios based on available space
const aspectRatioL = (5 / 12 * 100) / 16 * 9;
const aspectRatioM = 90 / 16 * 9;
const aspectRatioS = 100 / 16 * 9;

const styles = css`
  .video {
    width: 100%;
    position: relative;
    padding-bottom: ${aspectRatioS}%;
    height: 0;
    margin-top: ${vSpacing.m}px;
    overflow: hidden;
  }

  @media (min-width: ${screenSizes.xs}px) {
    .video {
      width: 90%;
      padding-bottom: ${aspectRatioM}%;
    }
  }

  @media (min-width: ${screenSizes.s}px) {
    .video {
      margin-top: 0;
      width: ${5 / 12 * 100}%;
      padding-bottom: ${aspectRatioL}%;
    }
  }

  iframe {
    position: absolute;
  }
`;

function useOnScreen(ref) {
    const [isIntersecting, setIntersecting] = useState(false);

    useEffect(() => {
        if (!window.IntersectionObserver) {
            setIntersecting(true);

            return;
        }

        if (!ref.current) { return; }

        const observer = new IntersectionObserver(
            ([entry]) => {
                entry.isIntersecting && setIntersecting(entry.isIntersecting);
            }
        );

        observer.observe(ref.current);
    }, [ref]);

    return isIntersecting;
}

export function RegularVideo({link, autoplay = true, title}) {
    const {url} = parseVideoUrl(link.href, autoplay);

    const ref = useRef(null);
    const isVisible = useOnScreen(ref);

    // Generate a meaningful title for the iframe
    const iframeTitle = title || 'Video content';

    return (
        <div className={'video'} ref={ref}>
            {
                isVisible && <iframe src={url}
                    title={iframeTitle}
                    frameBorder={'0'}
                    allow={'autoplay; fullscreen'}
                    style={{width: '100%', height: '100%'}}
                    allowFullScreen />
            }
            <style jsx>{styles}</style>
        </div>
    );
}

RegularVideo.propTypes = {
    link:     PropTypes.object,
    layout:   PropTypes.string,
    autoplay: PropTypes.bool,
    width:    PropTypes.number,
    height:   PropTypes.number,
    title:    PropTypes.string
};

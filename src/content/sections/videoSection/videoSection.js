import React from 'react';
import Head from 'next/head';
import PropTypes from 'prop-types';
import {css} from 'styled-jsx/css';
import _ from 'lodash';

import {screenSizes} from 'rf-styles';
import {generateVideoStructuredDataHtml} from 'rf-helpers';

import {Video} from './video';
import {Text} from './text';

const styles = css`
@media (min-width: ${screenSizes.s}px) { .reverse-section {
    flex-direction: row-reverse;
}}`;

function VideoSection({sendToRight, videoUrl, youtubeVideoData, ...props}) {
    const reverseClass = sendToRight ? '' : 'reverse-section';
    const structuredDataHtml = generateVideoStructuredDataHtml(youtubeVideoData);

    return (
        <>
            <section className={'page-horizontal-padding section-vertical-padding'} id={_.kebabCase(props.heading)}>
                <div className={`rf-flex-container center-content ${reverseClass}`}>
                    <Text {...props}/>
                    <Video link={videoUrl} title={props.heading}/>
                </div>
                <style jsx>{styles}</style>
            </section>
            {structuredDataHtml &&
            <Head>
                <script dangerouslySetInnerHTML={structuredDataHtml} type={'application/ld+json'}/>
            </Head>}
        </>
    );
}

VideoSection.propTypes = {
    heading:          PropTypes.string,
    content:          PropTypes.array,
    videoUrl:         PropTypes.object,
    youtubeVideoData: PropTypes.object,
    sendToRight:      PropTypes.bool,
    callToAction:     PropTypes.object,
    _key:             PropTypes.string,
    index:            PropTypes.number
};

export {VideoSection};

import React from 'react';
import PropTypes from 'prop-types';

import {RegularVideo} from './regularVideo';

export function Video(props) {
    if (!props.link) { return null; }

    return <RegularVideo {...props}/>;
}

Video.propTypes = {
    link:     PropTypes.object,
    layout:   PropTypes.string,
    autoplay: PropTypes.bool,
    width:    PropTypes.number,
    height:   PropTypes.number,
    title:    PropTypes.string
};

import React from 'react';

describe('imageTextSection', () => {
    const callToAction = {
        link:      'link',
        className: 'className',
        _key:      'key',
        title:     'title',
        route:     {}
    };

    const mockProps = {
        _key: 'some key',
        callToAction
    };

    test('renders without errors', () => {
        const {CtaSection} = require('../');

        const component = render(<CtaSection {...mockProps}/>);

        expect(component.toJSON()).toMatchSnapshot();
    });
});

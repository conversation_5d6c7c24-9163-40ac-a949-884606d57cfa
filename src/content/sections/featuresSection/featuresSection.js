import React from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import css from 'styled-jsx/css';

import {colors, screenSizes, vSpacing} from 'rf-styles';

import {Feature} from './feature';
import {FEATURE_SECTION_COLUMN_GAP} from './constants';

const styles = css`
  .feature-heading {
    text-align: center;
    margin-bottom: ${vSpacing.m}px;
  }

  .feature-container {
    display: flex;
    flex-flow: wrap;
    margin-bottom: -${vSpacing.l}px;
    row-gap: ${vSpacing.m}px;
    column-gap: ${FEATURE_SECTION_COLUMN_GAP}px;
  }

  @media (min-width: ${screenSizes.xs}px) {
    .feature-heading {
      margin-bottom: ${vSpacing.l}px;
    }

    .feature-container {
      flex-direction: row;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .feature-heading {
      margin-bottom: ${vSpacing.xl}px;
    }
  }
`;

export function FeaturesSection(props) {
    const {heading, headingHexColor, features} = props;
    const headerStyle = {color: headingHexColor ? `#${headingHexColor}` : colors.blue};

    return (
        <section className={'page-horizontal-padding section-vertical-padding'} id={_.kebabCase(heading)}>
            <h2 style={headerStyle}
                className={'h1 feature-heading'}>{heading}</h2>
            <div className={'feature-container'}>
                {_.map(features, feature => <Feature key={feature._key} headerStyle={headerStyle} {...feature} />)}
            </div>
            <style jsx>{styles}</style>
        </section>
    );
}

FeaturesSection.propTypes = {
    heading:         PropTypes.string,
    features:        PropTypes.array,
    headingHexColor: PropTypes.string
};

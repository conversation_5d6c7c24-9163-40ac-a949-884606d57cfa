// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FeaturesSection renders with a hexcolor 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id=""
>
  <h2
    className="h1 feature-heading"
    style={
      {
        "color": "#18609D",
      }
    }
  />
  <div
    className="feature-container"
  >
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#18609D",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#18609D",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#18609D",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#18609D",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

exports[`FeaturesSection renders without errors 1`] = `
<section
  className="page-horizontal-padding section-vertical-padding"
  id=""
>
  <h2
    className="h1 feature-heading"
    style={
      {
        "color": "#1976A3",
      }
    }
  />
  <div
    className="feature-container"
  >
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#1976A3",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#1976A3",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#1976A3",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
    <div
      className="feature "
    >
      <div
        className="icon"
      />
      <div
        className="text"
      >
        <h3
          className="h4"
          style={
            {
              "color": "#1976A3",
            }
          }
        />
        <p />
      </div>
      <style />
    </div>
  </div>
  <style />
</section>
`;

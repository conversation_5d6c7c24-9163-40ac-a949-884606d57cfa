import React from 'react';

describe('Feature', () => {
    let feature = {
        caption:     'FEATURE TITLE',
        description: 'feature description',
        icon:        {
            _type: 'image',
            asset: {
                _ref:  'image-dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6-1832x563-png',
                _type: 'reference'
            }
        },
        route: {
            _ref:  'page1',
            _type: 'reference',
            href:  'http://localhost:3000/v4/corsoroute'
        }
    };

    test('renders without errors', () => {
        let {Feature} = require('../feature');

        const component = render(<Feature {...feature}/>);

        expect(component).toMatchSnapshot();
    });

    test('All passed props are used', () => {
        let {Feature} = require('../feature');

        const component = render(<Feature {...feature}/>);

        const componentText = JSON.stringify(component.toJSON());

        expect(componentText.includes(feature.caption)).toBe(true);
        expect(componentText.includes(feature.description)).toBe(true);
        expect(componentText.includes('dfd2899567b73b8ac66757fdbcf3e43fc8d3e1c6')).toBe(true);
    });
});

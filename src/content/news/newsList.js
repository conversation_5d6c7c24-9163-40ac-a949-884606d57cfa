import * as _ from 'lodash';
import React from 'react';
import PropTypes from 'prop-types';
import css from 'styled-jsx/css';

import {T} from 'rf-i18n';
import {Article} from './article';
import {screenSizes, vSpacing} from 'rf-styles';

const styles = css`
  .news-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: ${vSpacing.m}px;
  }

  .list {
    box-sizing: border-box;
    display: flex;
    min-width: 320px;
    flex-direction: column;
    align-items: center;
    width: 83%;
    margin-top: -35px;
  }

  @media (min-width: ${screenSizes.s}px) {
    .list {
      width: 85%;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .list {
      width: 80%;
    }
  }

  @media (min-width: ${screenSizes.l}px) {
    .list {
      width: 66%;
    }
  }
  @media (min-width: ${screenSizes.xl}px) {
    .list {
      width: 57%;
    }
  }
  
  @media (min-width: ${screenSizes.xxl}px) {
    .list {
      width: 50%;
    }
  }
`;

export function NewsList({news}) {
    return (
        <div className={'news-list'}>

            <h1 className={'h2 center-content'}><T>sectionTitle.fleetsterNews</T></h1>

            <div className={'list'}>
                {_.map(news, article => <Article article={article} key={article._key}/>)}
            </div>
            <style jsx>{styles}</style>
        </div>
    );
}

NewsList.propTypes = {
    news: PropTypes.array
};

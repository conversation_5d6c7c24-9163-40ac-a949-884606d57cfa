import React from 'react';
import PropTypes from 'prop-types';
import moment from 'rf-moment-locale';
import css from 'styled-jsx/css';
import _ from 'lodash';

import {colors, fontWeights, screenSizes, vSpacing} from 'rf-styles';
import {removeEndSlash} from 'rf-utils';

import {config} from '../../../config';
import {NewsImage} from './newsImage';

const language = config.language;

export const styles = css`
  .article {
    display: flex;
    width: 100%;
    border-bottom: 1px solid ${colors.gray};
    text-decoration: none;
    font-weight: inherit;
    cursor: pointer;
    margin-bottom: 0px;
  }

  .article-wrapper {
    display: flex;
    margin-top: ${vSpacing.m}px;
    flex-direction: column;
    width: 100%;
  }

  .article:last-child {
    border-bottom: 1px solid ${colors.blue};
  }

  .description {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    margin-bottom: ${vSpacing.m}px;
  }
  
  .heading {
    font-weight: ${fontWeights.semiBold};
    margin-bottom: ${vSpacing.xs}px;
  }

  .published {
    height: 12px;
    color: ${colors.blue};
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1.2px;
    line-height: 12px;
  }

  @media (min-width: ${screenSizes.s}px) {
    .description {
      -webkit-line-clamp: 2;
    }
  }

  @media (min-width: ${screenSizes.m}px) {
    .description {
      margin-bottom: 0px;
    }
    .article-wrapper {
      display: flex;
      flex-direction: row;
    }
  }
`;

export function Article({article}) {
    const {heading, description, publishedOn, image, imageAlt, pageSlug} = article;
    const formattedDate = moment(publishedOn).locale(language).format('LL');

    return (
        <a className={'article'} href={`/${removeEndSlash(pageSlug)}`} id={_.kebabCase(heading)}>
            <div className={'article-wrapper'}>
                <NewsImage image={image} imageAlt={imageAlt}/>
                <div>
                    <h2 className={'h3 heading'}>{heading}</h2>
                    <p className={'published'}>{formattedDate}</p>
                    <p className={'description no-margin'}>{description}</p>
                </div>
            </div>
            <style jsx>{styles}</style>
        </a>
    );
}

Article.propTypes = {
    article: PropTypes.object
};

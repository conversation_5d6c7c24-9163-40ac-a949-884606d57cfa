import React from 'react';
describe('newsList', () => {
    const news = [
        {
            _key:        'c3cf0f2d1092',
            _type:       'articleHeadingSection',
            author:      'Ace Ventura',
            description: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor',
            heading:     'SOme heading',
            image:       {
                _type:  'image',
                asset:  {_ref: 'image-02e1c07cf4ac6a4440625717134715e0d1f71563-588x1242-png'},
                height: 1242,
                width:  588
            },
            imageAlt:    'My car',
            publishedOn: '2021-12-05',
            pageSlug:    'article-section',
            year:        2021,
            isIndex:     true
        },
        {
            _key:        '5b4dabcbae3c',
            _type:       'articleHeadingSection',
            author:      'Papichul<PERSON>',
            description: 'IM SO METAAAAAAAAAAAAAAAAAAA',
            heading:     'This is some news for you',
            image:       {
                _type:  'image',
                asset:  {_ref: 'image-02e1c07cf4ac6a4440625717134715e0d1f71563-588x1242-png'},
                height: 1242,
                width:  588
            },
            imageAlt:    'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempo',
            publishedOn: '2021-07-01',
            pageSlug:    'article-section',
            year:        2021,
            isIndex:     true
        },
        {
            _key:        'dceb7acefcfc',
            _type:       'articleHeadingSection',
            author:      'Chulo ',
            description: 'This is chulo\'s meta description don\'t mess with me  ',
            heading:     'First Heading',
            image:       {
                _type:  'image',
                asset:  {_ref: 'image-02e1c07cf4ac6a4440625717134715e0d1f71563-588x1242-png'},
                height: 1242,
                width:  588
            },
            imageAlt:    'Check yoself',
            publishedOn: '2021-06-29',
            pageSlug:    'wolobok',
            year:        2021,
            isIndex:     true
        }
    ];

    test('renders data correctly', () => {
        const {NewsList} = require('../newsList');
        const component = render(<NewsList news={news}/>);

        expect(component).toMatchSnapshot();
    });
});

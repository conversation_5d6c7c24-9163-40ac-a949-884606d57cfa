const getTranspilePackages = require('./getTranspilePackages');

function webpack(baseWebpackConfig) {
    baseWebpackConfig.module.rules.push(
        {
            test:  /\.svg$/,
            oneOf: [
                {
                    dependency: {not: ['url']},
                    use:        ['@svgr/webpack', 'new-url-loader']
                },
                {
                    type: 'asset'
                }
            ]
        }
    );

    return baseWebpackConfig;
}

const output = process.env.NODE_ENV !== 'production' ? 'standalone' : 'export';

module.exports = {
    output,
    transpilePackages: getTranspilePackages(),
    experimental:      {
        forceSwcTransforms: true // Todo remove when tests no longer depend on babel jest
    },
    eslint: {
        ignoreDuringBuilds: true
    },
    trailingSlash: true,
    webpack
};
